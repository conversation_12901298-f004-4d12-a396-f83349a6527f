#!/usr/bin/env python3
"""简单的DICOM文件读取器，不依赖pydicom"""

import struct
from pathlib import Path
from typing import Dict, Optional, Tuple

class SimpleDICOMReader:
    """简单的DICOM文件读取器"""
    
    def __init__(self):
        self.dicom_prefix = b'DICM'
    
    def is_dicom_file(self, file_path: Path) -> bool:
        """检查文件是否为DICOM格式"""
        try:
            with open(file_path, 'rb') as f:
                # 跳过前128字节的前导码
                f.seek(128)
                # 读取DICOM前缀
                prefix = f.read(4)
                return prefix == self.dicom_prefix
        except:
            return False
    
    def read_basic_info(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM文件的基本信息"""
        if not self.is_dicom_file(file_path):
            return None
        
        try:
            info = {
                'file_name': file_path.name,
                'file_size': file_path.stat().st_size,
                'is_dicom': True,
                'patient_id': 'Unknown',
                'modality': 'Unknown',
                'instance_number': 0,
                'rows': 512,  # 默认值
                'columns': 512,  # 默认值
                'has_pixel_data': True
            }
            
            # 尝试从文件名提取实例号
            import re
            numbers = re.findall(r'\d+', file_path.name)
            if numbers:
                info['instance_number'] = int(numbers[-1])
            
            return info
            
        except Exception as e:
            print(f"Error reading DICOM file {file_path}: {e}")
            return None
    
    def create_sample_image(self, width: int = 512, height: int = 512) -> bytes:
        """创建一个简单的示例图像数据"""
        import random
        
        # 创建简单的灰度图像数据
        image_data = bytearray()
        
        for y in range(height):
            for x in range(width):
                # 创建一个简单的渐变图案
                value = int((x + y) / (width + height) * 255)
                # 添加一些噪声
                value += random.randint(-20, 20)
                value = max(0, min(255, value))
                image_data.append(value)
        
        return bytes(image_data)
    
    def create_png_from_data(self, data: bytes, width: int, height: int) -> bytes:
        """将原始图像数据转换为PNG格式"""
        try:
            # 这里我们创建一个简单的PNG文件
            # 实际应用中应该使用PIL或其他图像库
            
            # 创建简单的灰度PNG头部
            png_signature = b'\x89PNG\r\n\x1a\n'
            
            # IHDR chunk
            ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 0, 0, 0, 0)
            ihdr_crc = self._crc32(b'IHDR' + ihdr_data)
            ihdr_chunk = struct.pack('>I', 13) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
            
            # 简化的IDAT chunk（实际应该压缩数据）
            idat_data = data
            idat_crc = self._crc32(b'IDAT' + idat_data)
            idat_chunk = struct.pack('>I', len(idat_data)) + b'IDAT' + idat_data + struct.pack('>I', idat_crc)
            
            # IEND chunk
            iend_crc = self._crc32(b'IEND')
            iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)
            
            return png_signature + ihdr_chunk + idat_chunk + iend_chunk
            
        except Exception as e:
            print(f"Error creating PNG: {e}")
            return b''
    
    def _crc32(self, data: bytes) -> int:
        """计算CRC32校验和"""
        import zlib
        return zlib.crc32(data) & 0xffffffff

# 测试函数
def test_dicom_reader():
    """测试DICOM读取器"""
    reader = SimpleDICOMReader()
    
    # 查找第一个DICOM文件
    data_path = Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics")
    
    if data_path.exists():
        print(f"数据目录: {data_path}")
        
        # 查找第一个患者目录
        patient_dirs = [d for d in data_path.iterdir() if d.is_dir()]
        if patient_dirs:
            first_patient = patient_dirs[0]
            print(f"患者目录: {first_patient.name}")
            
            # 查找第一个研究目录
            study_dirs = [d for d in first_patient.iterdir() if d.is_dir()]
            if study_dirs:
                first_study = study_dirs[0]
                print(f"研究目录: {first_study.name}")
                
                # 查找第一个序列目录
                series_dirs = [d for d in first_study.iterdir() if d.is_dir()]
                if series_dirs:
                    first_series = series_dirs[0]
                    print(f"序列目录: {first_series.name}")
                    
                    # 查找DICOM文件
                    dicom_files = [f for f in first_series.iterdir() if f.is_file()]
                    if dicom_files:
                        first_dicom = dicom_files[0]
                        print(f"DICOM文件: {first_dicom.name}")
                        
                        # 测试读取
                        if reader.is_dicom_file(first_dicom):
                            print("✅ 确认为DICOM文件")
                            info = reader.read_basic_info(first_dicom)
                            if info:
                                print(f"文件信息: {info}")
                        else:
                            print("❌ 不是DICOM文件")

if __name__ == "__main__":
    test_dicom_reader()
