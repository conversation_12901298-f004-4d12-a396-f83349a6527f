#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
from pathlib import Path
from typing import Dict, Optional, Tuple
import struct

# 真正的PyRadiomics库导入
try:
    import numpy as np
    import SimpleITK as sitk
    import radiomics
    from radiomics import featureextractor
    import pydicom
    REAL_PYRADIOMICS_AVAILABLE = True
    print("✅ 真正的PyRadiomics和DICOM库加载成功")
except ImportError as e:
    REAL_PYRADIOMICS_AVAILABLE = False
    print(f"⚠️ 真正的PyRadiomics库不可用: {e}")
    print("🔧 请安装: pip install pyradiomics numpy SimpleITK pydicom")

class RealPyRadiomicsDicomReader:
    """真正的PyRadiomics DICOM读取器"""
    
    def __init__(self, path: str):
        self.path = Path(path)
        self._dataset = None
        self._image = None
        self._array = None
        
    def read_dicom(self) -> Dict:
        """使用真正的PyRadiomics读取DICOM文件"""
        if not REAL_PYRADIOMICS_AVAILABLE:
            raise ImportError("真正的PyRadiomics库不可用")
            
        try:
            print(f"🚀 使用真正的PyRadiomics读取: {self.path.name}")
            
            # 1. 使用pydicom读取DICOM文件
            self._dataset = pydicom.dcmread(str(self.path))
            print(f"✅ PyDicom成功读取DICOM文件")
            
            # 2. 使用SimpleITK读取图像
            self._image = sitk.ReadImage(str(self.path))
            print(f"✅ SimpleITK成功读取图像: {self._image.GetSize()}")
            
            # 3. 转换为numpy数组
            self._array = sitk.GetArrayFromImage(self._image)
            print(f"✅ 转换为numpy数组: {self._array.shape}, 数据类型: {self._array.dtype}")
            
            # 4. 获取像素数据统计
            pixel_min = float(np.min(self._array))
            pixel_max = float(np.max(self._array))
            pixel_mean = float(np.mean(self._array))
            print(f"✅ 像素统计 - 最小值: {pixel_min}, 最大值: {pixel_max}, 平均值: {pixel_mean:.2f}")
            
            # 5. 获取DICOM元数据
            metadata = self._extract_metadata()
            
            # 6. 创建PyRadiomics特征提取器
            extractor = featureextractor.RadiomicsFeatureExtractor()
            print("✅ PyRadiomics特征提取器创建成功")
            
            return {
                'success': True,
                'reader': 'RealPyRadiomics',
                'image_shape': self._array.shape,
                'pixel_range': [pixel_min, pixel_max],
                'pixel_mean': pixel_mean,
                'metadata': metadata,
                'spacing': self._image.GetSpacing(),
                'origin': self._image.GetOrigin(),
                'direction': self._image.GetDirection()
            }
            
        except Exception as e:
            print(f"❌ 真正的PyRadiomics读取失败: {e}")
            raise
    
    def _extract_metadata(self) -> Dict:
        """提取DICOM元数据"""
        metadata = {}
        
        if self._dataset:
            # 基本信息
            metadata['PatientID'] = getattr(self._dataset, 'PatientID', 'Unknown')
            metadata['StudyInstanceUID'] = getattr(self._dataset, 'StudyInstanceUID', 'Unknown')
            metadata['SeriesInstanceUID'] = getattr(self._dataset, 'SeriesInstanceUID', 'Unknown')
            metadata['SOPInstanceUID'] = getattr(self._dataset, 'SOPInstanceUID', 'Unknown')
            
            # 图像信息
            metadata['Rows'] = getattr(self._dataset, 'Rows', 0)
            metadata['Columns'] = getattr(self._dataset, 'Columns', 0)
            metadata['PixelSpacing'] = getattr(self._dataset, 'PixelSpacing', [1.0, 1.0])
            metadata['SliceThickness'] = getattr(self._dataset, 'SliceThickness', 1.0)
            
            # 窗宽窗位
            metadata['WindowCenter'] = getattr(self._dataset, 'WindowCenter', None)
            metadata['WindowWidth'] = getattr(self._dataset, 'WindowWidth', None)
            
            # 设备信息
            metadata['Manufacturer'] = getattr(self._dataset, 'Manufacturer', 'Unknown')
            metadata['ManufacturerModelName'] = getattr(self._dataset, 'ManufacturerModelName', 'Unknown')
            
        return metadata
    
    def get_pixel_array(self):
        """获取像素数组"""
        if self._array is None:
            self.read_dicom()
        return self._array
    
    def create_image_base64(self, window_center: float = None, window_width: float = None) -> str:
        """创建Base64编码的图像"""
        try:
            if self._array is None:
                self.read_dicom()
            
            # 获取2D切片（如果是3D图像，取中间切片）
            if len(self._array.shape) == 3:
                slice_idx = self._array.shape[0] // 2
                image_2d = self._array[slice_idx]
            else:
                image_2d = self._array
            
            print(f"🎨 真正的PyRadiomics创建图像: 形状={image_2d.shape}")
            
            # 应用窗宽窗位
            if window_center is not None and window_width is not None:
                print(f"🎨 应用窗宽窗位: WC={window_center}, WW={window_width}")
                min_val = window_center - window_width / 2
                max_val = window_center + window_width / 2
                image_2d = np.clip(image_2d, min_val, max_val)
                image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
            else:
                # 自动缩放到0-255
                min_val = np.min(image_2d)
                max_val = np.max(image_2d)
                if max_val > min_val:
                    image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
                else:
                    image_2d = np.zeros_like(image_2d, dtype=np.uint8)
            
            # 转换为PIL图像格式
            from PIL import Image
            import io
            
            # 确保是2D图像
            if len(image_2d.shape) > 2:
                image_2d = image_2d[:, :, 0]
            
            # 创建PIL图像
            pil_image = Image.fromarray(image_2d, mode='L')
            
            # 转换为Base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            print(f"✅ 真正的PyRadiomics图像创建成功: {len(image_base64)} 字符")
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            print(f"❌ 真正的PyRadiomics图像创建失败: {e}")
            raise

def read_dicom_with_real_pyradiomics(file_path: str, window_center: float = None, window_width: float = None) -> str:
    """使用真正的PyRadiomics读取DICOM并返回Base64图像"""
    try:
        print(f"🚀 启动真正的PyRadiomics DICOM读取器")
        
        if not REAL_PYRADIOMICS_AVAILABLE:
            raise ImportError("真正的PyRadiomics库不可用，请先安装相关依赖")
        
        reader = RealPyRadiomicsDicomReader(file_path)
        info = reader.read_dicom()
        
        print(f"✅ 真正的PyRadiomics读取成功:")
        print(f"   - 图像形状: {info['image_shape']}")
        print(f"   - 像素范围: {info['pixel_range']}")
        print(f"   - 像素平均值: {info['pixel_mean']:.2f}")
        print(f"   - 图像间距: {info['spacing']}")
        
        # 创建图像
        image_base64 = reader.create_image_base64(window_center, window_width)
        
        return image_base64
        
    except Exception as e:
        print(f"❌ 真正的PyRadiomics处理失败: {e}")
        raise

if __name__ == "__main__":
    # 测试代码
    if REAL_PYRADIOMICS_AVAILABLE:
        print("✅ 真正的PyRadiomics DICOM读取器准备就绪")
    else:
        print("⚠️ 真正的PyRadiomics库不可用")
        print("请运行: pip install pyradiomics numpy SimpleITK pydicom")
