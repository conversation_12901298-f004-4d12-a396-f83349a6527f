import http.server
import socketserver
import json
import pandas as pd
from pathlib import Path
from urllib.parse import urlparse, parse_qs
import os

# 数据路径配置
DICOM_DATA_PATH = Path("../sourcedata/a/manifest-1603198545583")
METADATA_FILE = DICOM_DATA_PATH / "metadata.csv"

class DICOMHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.metadata_df = None
        self.load_metadata()
        super().__init__(*args, **kwargs)
    
    def load_metadata(self):
        """加载元数据CSV文件"""
        try:
            if METADATA_FILE.exists():
                self.metadata_df = pd.read_csv(METADATA_FILE)
                print(f"Loaded metadata with {len(self.metadata_df)} records")
            else:
                print(f"Metadata file not found: {METADATA_FILE}")
        except Exception as e:
            print(f"Error loading metadata: {e}")
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        try:
            if path == '/api/patients':
                response = self.get_patients()
            elif path.startswith('/api/patients/') and path.endswith('/studies'):
                subject_id = path.split('/')[-2]
                response = self.get_patient_studies(subject_id)
            elif path.startswith('/api/studies/') and path.endswith('/series'):
                study_uid = path.split('/')[-2]
                response = self.get_study_series(study_uid)
            elif path == '/api/metadata/stats':
                response = self.get_metadata_stats()
            else:
                response = {"error": "Not found"}
            
            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def get_patients(self):
        """获取所有患者列表"""
        if self.metadata_df is None:
            return {"patients": [], "total": 0}
        
        patients = []
        for subject_id in self.metadata_df['Subject ID'].unique():
            patient_data = self.metadata_df[self.metadata_df['Subject ID'] == subject_id]
            patients.append({
                'subject_id': subject_id,
                'study_count': len(patient_data['Study UID'].unique()),
                'series_count': len(patient_data),
                'modalities': list(patient_data['Modality'].unique()),
                'study_dates': list(patient_data['Study Date'].unique())
            })
        
        return {"patients": sorted(patients, key=lambda x: x['subject_id']), "total": len(patients)}
    
    def get_patient_studies(self, subject_id):
        """获取特定患者的研究列表"""
        if self.metadata_df is None:
            return {"studies": [], "subject_id": subject_id}
        
        patient_data = self.metadata_df[self.metadata_df['Subject ID'] == subject_id]
        studies = []
        
        for study_uid in patient_data['Study UID'].unique():
            study_data = patient_data[patient_data['Study UID'] == study_uid]
            studies.append({
                'study_uid': study_uid,
                'study_date': study_data['Study Date'].iloc[0],
                'study_description': study_data['Study Description'].iloc[0],
                'series_count': len(study_data),
                'modalities': list(study_data['Modality'].unique())
            })
        
        return {"studies": studies, "subject_id": subject_id}
    
    def get_study_series(self, study_uid):
        """获取特定研究的序列列表"""
        if self.metadata_df is None:
            return {"series": [], "study_uid": study_uid}
        
        study_data = self.metadata_df[self.metadata_df['Study UID'] == study_uid]
        series = []
        
        for _, row in study_data.iterrows():
            series.append({
                'series_uid': row['Series UID'],
                'series_description': row['Series Description'],
                'modality': row['Modality'],
                'manufacturer': row['Manufacturer'],
                'sop_class_name': row['SOP Class Name'],
                'number_of_images': row['Number of Images'],
                'file_size': row['File Size'],
                'file_location': row['File Location']
            })
        
        return {"series": series, "study_uid": study_uid}
    
    def get_metadata_stats(self):
        """获取元数据统计信息"""
        if self.metadata_df is None:
            return {"error": "Metadata not loaded"}
        
        df = self.metadata_df
        stats = {
            "total_patients": len(df['Subject ID'].unique()),
            "total_studies": len(df['Study UID'].unique()),
            "total_series": len(df),
            "modalities": df['Modality'].value_counts().to_dict(),
            "manufacturers": df['Manufacturer'].value_counts().to_dict(),
            "date_range": {
                "earliest": df['Study Date'].min(),
                "latest": df['Study Date'].max()
            }
        }
        return stats

if __name__ == "__main__":
    PORT = 8000
    
    # 创建处理器实例
    handler = DICOMHandler
    
    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"DICOM API Server running on http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")
