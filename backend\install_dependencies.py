#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"🔧 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   错误: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package_name} 未安装")
        return False

def main():
    """主安装流程"""
    print("🚀 开始安装PyRadiomics依赖库...")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("numpy", "numpy"),
        ("Pillow", "PIL"),
        ("pydicom", "pydicom"),
        ("SimpleITK", "SimpleITK"),
        ("pyradiomics", "radiomics")
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        print(f"\n📦 处理 {package_name}...")
        
        # 检查是否已安装
        if check_package(package_name, import_name):
            installed_count += 1
            continue
        
        # 尝试安装
        if install_package(package_name):
            # 再次检查是否安装成功
            if check_package(package_name, import_name):
                installed_count += 1
            else:
                print(f"⚠️ {package_name} 安装后仍无法导入")
        else:
            print(f"❌ {package_name} 安装失败")
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {installed_count}/{total_count} 个包安装成功")
    
    if installed_count == total_count:
        print("🎉 所有依赖库安装成功！")
        print("✅ PyRadiomics现在应该可以使用了")
        
        # 测试PyRadiomics
        print("\n🧪 测试PyRadiomics...")
        try:
            import radiomics
            from radiomics import featureextractor
            import numpy as np
            import SimpleITK as sitk
            import pydicom
            print("✅ PyRadiomics测试成功！")
            print(f"   - PyRadiomics版本: {radiomics.__version__}")
            print(f"   - NumPy版本: {np.__version__}")
            return True
        except Exception as e:
            print(f"❌ PyRadiomics测试失败: {e}")
            return False
    else:
        print("⚠️ 部分依赖库安装失败")
        print("💡 建议手动安装失败的包:")
        for package_name, import_name in packages:
            if not check_package(package_name, import_name):
                print(f"   pip install {package_name}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 下一步: 重启DICOM服务器以使用真正的PyRadiomics")
        print("   cd backend && python basic_server.py")
    else:
        print("\n🔧 如果安装失败，请尝试:")
        print("   1. 更新pip: python -m pip install --upgrade pip")
        print("   2. 手动安装: pip install numpy Pillow pydicom SimpleITK pyradiomics")
        print("   3. 检查网络连接和防火墙设置")
