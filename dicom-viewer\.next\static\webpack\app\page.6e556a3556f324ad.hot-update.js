"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nfunction Home() {\n    var _seriesImages_images_currentImageIndex_pixel_spacing;\n    _s();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studies, setStudies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedStudy, setSelectedStudy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [series, setSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSeries, setSelectedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [seriesImages, setSeriesImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [windowCenter, setWindowCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageViewer, setShowImageViewer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE = 'http://localhost:8000';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchPatients();\n            fetchStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const fetchPatients = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients\"));\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const data = await response.json();\n            setPatients(data.patients);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/metadata/stats\"));\n            if (!response.ok) throw new Error('Failed to fetch stats');\n            const data = await response.json();\n            setStats(data);\n        } catch (err) {\n            console.error('Failed to fetch stats:', err);\n        }\n    };\n    const fetchStudies = async (subjectId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients/\").concat(subjectId, \"/studies\"));\n            if (!response.ok) throw new Error('Failed to fetch studies');\n            const data = await response.json();\n            setStudies(data.studies);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeries = async (studyUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/studies/\").concat(studyUid, \"/series\"));\n            if (!response.ok) throw new Error('Failed to fetch series');\n            const data = await response.json();\n            setSeries(data.series);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeriesImages = async (seriesUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/series/\").concat(seriesUid, \"/images\"));\n            if (!response.ok) throw new Error('Failed to fetch series images');\n            const data = await response.json();\n            setSeriesImages(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const handlePatientSelect = (patient)=>{\n        setSelectedPatient(patient);\n        setSelectedStudy(null);\n        setSeries([]);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchStudies(patient.subject_id);\n    };\n    const handleStudySelect = (study)=>{\n        setSelectedStudy(study);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchSeries(study.study_uid);\n    };\n    const handleSeriesSelect = (series)=>{\n        setSelectedSeries(series);\n        setShowImageViewer(true);\n        fetchSeriesImages(series.series_uid);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"正在加载DICOM数据...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl text-red-600\",\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"DICOM 影像查看器\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"患者: \",\n                                                    stats.total_patients\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"研究: \",\n                                                    stats.total_studies\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"序列: \",\n                                                    stats.total_series\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/analysis\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                        children: \"数据分析\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"患者列表 (\",\n                                            patients.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.subject_id) === patient.subject_id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handlePatientSelect(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: patient.subject_id\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            patient.study_count,\n                                                            \" 个研究, \",\n                                                            patient.series_count,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: patient.modalities.join(', ')\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, patient.subject_id, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"研究列表 \",\n                                            selectedPatient && \"(\".concat(selectedPatient.subject_id, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedPatient ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: studies.map((study)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedStudy === null || selectedStudy === void 0 ? void 0 : selectedStudy.study_uid) === study.study_uid ? 'bg-green-100 border-green-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleStudySelect(study),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: study.study_date\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: study.study_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            study.series_count,\n                                                            \" 个序列 - \",\n                                                            study.modalities.join(', ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, study.study_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择患者以查看研究\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"序列列表 \",\n                                            selectedStudy && \"(\".concat(selectedStudy.study_date, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedStudy ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: series.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedSeries === null || selectedSeries === void 0 ? void 0 : selectedSeries.series_uid) === s.series_uid ? 'bg-purple-100 border-purple-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleSeriesSelect(s),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: s.modality\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: s.series_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            s.number_of_images,\n                                                            \" 张图像 - \",\n                                                            s.file_size\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: s.manufacturer\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, s.series_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择研究以查看序列\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"数据集统计\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"成像模态\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.modalities).map((param)=>{\n                                                    let [modality, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: modality\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, modality, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"设备制造商\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.manufacturers).slice(0, 5).map((param)=>{\n                                                    let [manufacturer, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: manufacturer\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, manufacturer, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"起始: \",\n                                                            stats.date_range.earliest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"结束: \",\n                                                            stats.date_range.latest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_patients,\n                                                            \" 名患者\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_studies,\n                                                            \" 个研究\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_series,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this),\n                    showImageViewer && selectedSeries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center p-4 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                selectedSeries.modality,\n                                                \" - \",\n                                                selectedSeries.series_description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageViewer(false),\n                                            className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 max-h-[80vh] overflow-y-auto\",\n                                    children: seriesImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"序列信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-3 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"成像模态:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.modality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"图像总数:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.total_images\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"序列描述:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.description\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"设备制造商:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.manufacturer\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"图像查看器\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    seriesImages.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMImageViewer, {\n                                                                    image: seriesImages.images[currentImageIndex],\n                                                                    apiBase: API_BASE\n                                                                }, void 0, false, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.max(0, currentImageIndex - 1)),\n                                                                        disabled: currentImageIndex === 0,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"上一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            currentImageIndex + 1,\n                                                                            \" / \",\n                                                                            seriesImages.images.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.min(seriesImages.images.length - 1, currentImageIndex + 1)),\n                                                                        disabled: currentImageIndex === seriesImages.images.length - 1,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"下一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-3 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"文件名: \",\n                                                                                seriesImages.images[currentImageIndex].file_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"大小: \",\n                                                                                (seriesImages.images[currentImageIndex].file_size / 1024).toFixed(1),\n                                                                                \" KB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        seriesImages.images[currentImageIndex].is_dicom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"实例号: \",\n                                                                                        seriesImages.images[currentImageIndex].instance_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 441,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"图像尺寸: \",\n                                                                                        seriesImages.images[currentImageIndex].rows,\n                                                                                        \" \\xd7 \",\n                                                                                        seriesImages.images[currentImageIndex].columns\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 442,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"像素间距: \",\n                                                                                        (_seriesImages_images_currentImageIndex_pixel_spacing = seriesImages.images[currentImageIndex].pixel_spacing) === null || _seriesImages_images_currentImageIndex_pixel_spacing === void 0 ? void 0 : _seriesImages_images_currentImageIndex_pixel_spacing.join(' × '),\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 443,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"层厚: \",\n                                                                                        seriesImages.images[currentImageIndex].slice_thickness,\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                                        children: \"缩略图导航\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                                                        children: seriesImages.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 cursor-pointer border-2 rounded \".concat(index === currentImageIndex ? 'border-blue-500' : 'border-gray-300'),\n                                                                                onClick: ()=>setCurrentImageIndex(index),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMThumbnail, {\n                                                                                    image: image,\n                                                                                    apiBase: API_BASE,\n                                                                                    size: 64\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, image.image_index, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg\",\n                                            children: \"正在加载图像...\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"uSZvbZqGQMA2MHRJKeTvi163clU=\");\n_c = Home;\n// DICOM图像查看器组件\nfunction DICOMImageViewer(param) {\n    let { image, apiBase } = param;\n    _s1();\n    const [imageData, setImageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMImageViewer.useEffect\": ()=>{\n            if (image.is_dicom && image.image_url) {\n                setLoading(true);\n                setError(null);\n                fetch(\"\".concat(apiBase).concat(image.image_url)).then({\n                    \"DICOMImageViewer.useEffect\": (response)=>response.json()\n                }[\"DICOMImageViewer.useEffect\"]).then({\n                    \"DICOMImageViewer.useEffect\": (data)=>{\n                        if (data.success && data.image_data) {\n                            setImageData(data.image_data);\n                        } else {\n                            setError(data.error || '无法加载图像');\n                        }\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).catch({\n                    \"DICOMImageViewer.useEffect\": (err)=>{\n                        setError('加载图像时出错');\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).finally({\n                    \"DICOMImageViewer.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMImageViewer.useEffect\"]);\n            } else {\n                setLoading(false);\n                setError('非DICOM文件或无图像数据');\n            }\n        }\n    }[\"DICOMImageViewer.useEffect\"], [\n        image,\n        apiBase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"正在加载图像...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 524,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 523,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 532,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 531,\n            columnNumber: 7\n        }, this);\n    }\n    if (imageData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: imageData,\n                alt: \"DICOM图像 \".concat(image.file_name),\n                className: \"max-w-full max-h-96 object-contain\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 540,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-96 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"无图像数据\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 551,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\n_s1(DICOMImageViewer, \"KEnvJThExdJ31O6VITgjvSJA/AQ=\");\n_c1 = DICOMImageViewer;\n// DICOM缩略图组件\nfunction DICOMThumbnail(param) {\n    let { image, apiBase, size = 64 } = param;\n    _s2();\n    const [thumbnailData, setThumbnailData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMThumbnail.useEffect\": ()=>{\n            if (image.is_dicom && image.thumbnail_url) {\n                fetch(\"\".concat(apiBase).concat(image.thumbnail_url)).then({\n                    \"DICOMThumbnail.useEffect\": (response)=>response.json()\n                }[\"DICOMThumbnail.useEffect\"]).then({\n                    \"DICOMThumbnail.useEffect\": (data)=>{\n                        if (data.success && data.thumbnail_data) {\n                            setThumbnailData(data.thumbnail_data);\n                        }\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).catch({\n                    \"DICOMThumbnail.useEffect\": (err)=>{\n                        console.error('加载缩略图时出错:', err);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).finally({\n                    \"DICOMThumbnail.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]);\n            } else {\n                setLoading(false);\n            }\n        }\n    }[\"DICOMThumbnail.useEffect\"], [\n        image,\n        apiBase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n            style: {\n                width: size,\n                height: size\n            },\n            children: \"加载中...\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 583,\n            columnNumber: 7\n        }, this);\n    }\n    if (thumbnailData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: thumbnailData,\n            alt: \"缩略图 \".concat(image.file_name),\n            style: {\n                width: size,\n                height: size\n            },\n            className: \"object-cover\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 594,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n        style: {\n            width: size,\n            height: size\n        },\n        children: image.image_index + 1\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 604,\n        columnNumber: 5\n    }, this);\n}\n_s2(DICOMThumbnail, \"OFgfWwmGhuZAeKYBmxFIBQq2c+4=\");\n_c2 = DICOMThumbnail;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"DICOMImageViewer\");\n$RefreshReg$(_c2, \"DICOMThumbnail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});