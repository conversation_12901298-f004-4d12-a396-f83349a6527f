#!/usr/bin/env python3
"""PyRadiomics安装脚本"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"🔧 {description}")
    print(f"{'='*50}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("✅ 输出:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功完成")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def check_python_environment():
    """检查Python环境"""
    print("🐍 检查Python环境")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")

def install_dependencies():
    """安装依赖"""
    dependencies = [
        ("python -m pip install --upgrade pip", "升级pip"),
        ("pip install numpy", "安装numpy"),
        ("pip install SimpleITK", "安装SimpleITK"),
        ("pip install pyradiomics", "安装PyRadiomics"),
        ("pip install pillow", "安装Pillow"),
        ("pip install pydicom", "安装pydicom")
    ]
    
    success_count = 0
    for command, description in dependencies:
        if run_command(command, description):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{len(dependencies)} 成功")
    return success_count == len(dependencies)

def test_imports():
    """测试导入"""
    print(f"\n{'='*50}")
    print("🧪 测试库导入")
    print(f"{'='*50}")
    
    libraries = [
        ("numpy", "import numpy as np; print(f'numpy版本: {np.__version__}')"),
        ("SimpleITK", "import SimpleITK as sitk; print(f'SimpleITK版本: {sitk.Version.VersionString()}')"),
        ("pyradiomics", "import radiomics; print(f'PyRadiomics版本: {radiomics.__version__}')"),
        ("PIL", "from PIL import Image; print(f'Pillow版本: {Image.__version__}')"),
        ("pydicom", "import pydicom; print(f'pydicom版本: {pydicom.__version__}')")
    ]
    
    success_count = 0
    for name, import_code in libraries:
        try:
            exec(import_code)
            print(f"✅ {name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} 导入失败: {e}")
        except Exception as e:
            print(f"⚠️ {name} 导入异常: {e}")
    
    print(f"\n📊 导入结果: {success_count}/{len(libraries)} 成功")
    return success_count == len(libraries)

def test_pyradiomics_functionality():
    """测试PyRadiomics功能"""
    print(f"\n{'='*50}")
    print("🔬 测试PyRadiomics功能")
    print(f"{'='*50}")
    
    try:
        import SimpleITK as sitk
        import radiomics
        from radiomics import featureextractor
        
        print("✅ 成功导入所有PyRadiomics组件")
        
        # 创建特征提取器
        extractor = featureextractor.RadiomicsFeatureExtractor()
        print("✅ 成功创建特征提取器")
        
        # 创建简单的测试图像
        test_image = sitk.GetImageFromArray([[1, 2], [3, 4]])
        test_mask = sitk.GetImageFromArray([[1, 1], [1, 1]])
        
        print("✅ 成功创建测试图像")
        
        # 测试特征提取
        features = extractor.execute(test_image, test_mask)
        print(f"✅ 成功提取特征: {len(features)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ PyRadiomics功能测试失败: {e}")
        return False

def create_pyradiomics_test():
    """创建PyRadiomics测试文件"""
    test_code = '''#!/usr/bin/env python3
"""PyRadiomics测试"""

try:
    import numpy as np
    import SimpleITK as sitk
    import radiomics
    from radiomics import featureextractor
    
    print("✅ 所有库导入成功")
    print(f"numpy版本: {np.__version__}")
    print(f"SimpleITK版本: {sitk.Version.VersionString()}")
    print(f"PyRadiomics版本: {radiomics.__version__}")
    
    # 创建特征提取器
    extractor = featureextractor.RadiomicsFeatureExtractor()
    print("✅ 特征提取器创建成功")
    
    # 创建测试数据
    image_array = np.random.randint(0, 100, (10, 10, 10))
    mask_array = np.ones((10, 10, 10))
    
    image = sitk.GetImageFromArray(image_array)
    mask = sitk.GetImageFromArray(mask_array)
    
    print("✅ 测试数据创建成功")
    
    # 提取特征
    features = extractor.execute(image, mask)
    print(f"✅ 特征提取成功: {len(features)} 个特征")
    
    # 显示一些特征
    for i, (key, value) in enumerate(features.items()):
        if i < 5:  # 只显示前5个
            print(f"   {key}: {value}")
    
    print("🎉 PyRadiomics完全正常工作！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 运行错误: {e}")
'''
    
    with open("test_pyradiomics.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("✅ 创建PyRadiomics测试文件: test_pyradiomics.py")

def main():
    """主函数"""
    print("🚀 PyRadiomics强制安装程序")
    print("=" * 50)
    
    # 检查环境
    check_python_environment()
    
    # 安装依赖
    if install_dependencies():
        print("\n🎉 所有依赖安装成功！")
    else:
        print("\n⚠️ 部分依赖安装失败，继续测试...")
    
    # 测试导入
    if test_imports():
        print("\n🎉 所有库导入成功！")
    else:
        print("\n⚠️ 部分库导入失败...")
    
    # 测试PyRadiomics功能
    if test_pyradiomics_functionality():
        print("\n🎉 PyRadiomics功能测试成功！")
    else:
        print("\n⚠️ PyRadiomics功能测试失败...")
    
    # 创建测试文件
    create_pyradiomics_test()
    
    print(f"\n{'='*50}")
    print("📋 安装完成总结")
    print(f"{'='*50}")
    print("请运行以下命令测试PyRadiomics:")
    print("python test_pyradiomics.py")
    print("\n如果测试成功，PyRadiomics已准备就绪！")

if __name__ == "__main__":
    main()
