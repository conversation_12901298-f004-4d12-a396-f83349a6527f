@echo off
echo 🎯 启动PyRadiomics DICOM服务器
echo ================================

REM 激活conda环境
echo 📦 激活pyradiomics环境...
call conda activate pyradiomics

REM 检查PyRadiomics是否安装
echo 🔍 检查PyRadiomics安装...
python -c "import pyradiomics; print('✅ PyRadiomics版本:', pyradiomics.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyRadiomics未安装，正在安装...
    pip install pyradiomics pydicom numpy Pillow SimpleITK
)

REM 启动服务器
echo 🚀 启动纯PyRadiomics服务器...
python pure_pyradiomics_server.py

pause
