@echo off
echo 🎯 启动PyRadiomics DICOM服务器 (Python 3.9)
echo =============================================

REM 检查是否有conda
where conda >nul 2>&1
if %errorlevel% == 0 (
    echo 📦 使用conda创建Python 3.9环境...
    call conda create -n pyradiomics python=3.9 -y
    call conda activate pyradiomics
) else (
    echo 📦 使用Python 3.9创建虚拟环境...
    if exist "C:\Python39\python.exe" (
        C:\Python39\python.exe -m venv pyradiomics_env39
        call pyradiomics_env39\Scripts\activate
    ) else (
        echo ❌ 未找到Python 3.9，请先安装Python 3.9.18
        echo 下载地址: https://www.python.org/downloads/release/python-3918/
        pause
        exit /b 1
    )
)

REM 验证Python版本
echo 🔍 验证Python版本...
python --version

REM 检查PyRadiomics是否安装
echo 🔍 检查PyRadiomics安装...
python -c "import pyradiomics; print('✅ PyRadiomics版本:', pyradiomics.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyRadiomics未安装，正在安装...
    pip install pyradiomics pydicom numpy Pillow SimpleITK
)

REM 启动服务器
echo 🚀 启动纯PyRadiomics服务器...
python pure_pyradiomics_server.py

pause
