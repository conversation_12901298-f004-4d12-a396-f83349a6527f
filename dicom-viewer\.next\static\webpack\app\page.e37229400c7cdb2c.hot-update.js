"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    _s();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studies, setStudies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedStudy, setSelectedStudy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [series, setSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSeries, setSelectedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [seriesImages, setSeriesImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageViewer, setShowImageViewer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE = 'http://localhost:8000';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchPatients();\n            fetchStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const fetchPatients = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients\"));\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const data = await response.json();\n            setPatients(data.patients);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/metadata/stats\"));\n            if (!response.ok) throw new Error('Failed to fetch stats');\n            const data = await response.json();\n            setStats(data);\n        } catch (err) {\n            console.error('Failed to fetch stats:', err);\n        }\n    };\n    const fetchStudies = async (subjectId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients/\").concat(subjectId, \"/studies\"));\n            if (!response.ok) throw new Error('Failed to fetch studies');\n            const data = await response.json();\n            setStudies(data.studies);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeries = async (studyUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/studies/\").concat(studyUid, \"/series\"));\n            if (!response.ok) throw new Error('Failed to fetch series');\n            const data = await response.json();\n            setSeries(data.series);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeriesImages = async (seriesUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/series/\").concat(seriesUid, \"/images\"));\n            if (!response.ok) throw new Error('Failed to fetch series images');\n            const data = await response.json();\n            setSeriesImages(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const handlePatientSelect = (patient)=>{\n        setSelectedPatient(patient);\n        setSelectedStudy(null);\n        setSeries([]);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchStudies(patient.subject_id);\n    };\n    const handleStudySelect = (study)=>{\n        setSelectedStudy(study);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchSeries(study.study_uid);\n    };\n    const handleSeriesSelect = (series)=>{\n        setSelectedSeries(series);\n        setShowImageViewer(true);\n        fetchSeriesImages(series.series_uid);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"正在加载DICOM数据...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl text-red-600\",\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"DICOM 影像查看器\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"患者: \",\n                                                    stats.total_patients\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"研究: \",\n                                                    stats.total_studies\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"序列: \",\n                                                    stats.total_series\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/analysis\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                        children: \"数据分析\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"患者列表 (\",\n                                            patients.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.subject_id) === patient.subject_id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handlePatientSelect(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: patient.subject_id\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            patient.study_count,\n                                                            \" 个研究, \",\n                                                            patient.series_count,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: patient.modalities.join(', ')\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, patient.subject_id, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"研究列表 \",\n                                            selectedPatient && \"(\".concat(selectedPatient.subject_id, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedPatient ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: studies.map((study)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedStudy === null || selectedStudy === void 0 ? void 0 : selectedStudy.study_uid) === study.study_uid ? 'bg-green-100 border-green-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleStudySelect(study),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: study.study_date\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: study.study_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            study.series_count,\n                                                            \" 个序列 - \",\n                                                            study.modalities.join(', ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, study.study_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择患者以查看研究\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"序列列表 \",\n                                            selectedStudy && \"(\".concat(selectedStudy.study_date, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedStudy ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: series.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedSeries === null || selectedSeries === void 0 ? void 0 : selectedSeries.series_uid) === s.series_uid ? 'bg-purple-100 border-purple-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleSeriesSelect(s),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: s.modality\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: s.series_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            s.number_of_images,\n                                                            \" 张图像 - \",\n                                                            s.file_size\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: s.manufacturer\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, s.series_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择研究以查看序列\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"数据集统计\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"成像模态\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.modalities).map((param)=>{\n                                                    let [modality, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: modality\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, modality, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"设备制造商\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.manufacturers).slice(0, 5).map((param)=>{\n                                                    let [manufacturer, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: manufacturer\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, manufacturer, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"起始: \",\n                                                            stats.date_range.earliest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"结束: \",\n                                                            stats.date_range.latest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_patients,\n                                                            \" 名患者\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_studies,\n                                                            \" 个研究\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_series,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    showImageViewer && selectedSeries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center p-4 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                selectedSeries.modality,\n                                                \" - \",\n                                                selectedSeries.series_description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageViewer(false),\n                                            className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 max-h-[80vh] overflow-y-auto\",\n                                    children: seriesImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"序列信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-3 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"成像模态:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.modality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"图像总数:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.total_images\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"序列描述:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.description\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"设备制造商:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.manufacturer\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: [\n                                                            \"图像列表 (\",\n                                                            seriesImages.images.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                        children: seriesImages.images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-3 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: [\n                                                                            \"图像 \",\n                                                                            image.image_index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"文件名: \",\n                                                                                    image.file_name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"大小: \",\n                                                                                    (image.file_size / 1024).toFixed(1),\n                                                                                    \" KB\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 bg-gray-200 h-32 rounded flex items-center justify-center text-gray-500 text-sm\",\n                                                                        children: [\n                                                                            \"DICOM 图像预览\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"(需要DICOM查看器)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, image.image_index, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg\",\n                                            children: \"正在加载图像...\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"hWm118uS6mcbmgQzTnUmhtIzXGo=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});