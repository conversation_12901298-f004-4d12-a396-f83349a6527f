#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
纯PyRadiomics DICOM服务器
只使用真正的PyRadiomics库，不使用任何模拟器或基础读取器
"""

import json
import os
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import traceback

# 导入纯PyRadiomics处理器
try:
    from pure_pyradiomics_processor import PurePyRadiomicsProcessor
    print("✅ 纯PyRadiomics处理器加载成功")
except ImportError as e:
    print(f"❌ 无法加载纯PyRadiomics处理器: {e}")
    print("请确保已安装: pip install pyradiomics pydicom numpy Pillow SimpleITK")
    exit(1)

# 数据路径配置
DATA_PATH = Path("../sourcedata")
if not DATA_PATH.exists():
    print(f"⚠️ 数据路径不存在: {DATA_PATH}")
    print("请确保DICOM数据在正确的路径")

# 初始化处理器
processor = PurePyRadiomicsProcessor(DATA_PATH)

class PurePyRadiomicsHandler(BaseHTTPRequestHandler):
    """纯PyRadiomics HTTP请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            # 设置CORS头
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            # 路由处理
            if path == '/api/patients':
                self.handle_patients()
            elif path.startswith('/api/patients/') and path.endswith('/studies'):
                patient_id = path.split('/')[-2]
                self.handle_studies(patient_id)
            elif path.startswith('/api/studies/') and path.endswith('/series'):
                study_uid = path.split('/')[-2]
                self.handle_series(study_uid)
            elif path.startswith('/api/series/') and path.endswith('/images'):
                series_uid = path.split('/')[-2]
                self.handle_images(series_uid)
            elif path.startswith('/api/dicom/image/'):
                self.handle_dicom_image(path, query_params)
            elif path.startswith('/api/dicom/thumbnail/'):
                self.handle_dicom_thumbnail(path, query_params)
            elif path == '/api/metadata/stats':
                self.handle_metadata_stats()
            else:
                self.send_error_response(404, "API endpoint not found")
                
        except Exception as e:
            print(f"❌ 请求处理错误: {e}")
            traceback.print_exc()
            self.send_error_response(500, str(e))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_patients(self):
        """处理患者列表请求"""
        try:
            print("📋 获取患者列表")
            patients = processor.get_patients()
            self.send_json_response(patients)
        except Exception as e:
            self.send_error_response(500, f"获取患者列表失败: {e}")
    
    def handle_studies(self, patient_id: str):
        """处理研究列表请求"""
        try:
            print(f"📋 获取患者 {patient_id} 的研究列表")
            studies = processor.get_studies(patient_id)
            self.send_json_response(studies)
        except Exception as e:
            self.send_error_response(500, f"获取研究列表失败: {e}")
    
    def handle_series(self, study_uid: str):
        """处理序列列表请求"""
        try:
            print(f"📋 获取研究 {study_uid} 的序列列表")
            series = processor.get_series(study_uid)
            self.send_json_response(series)
        except Exception as e:
            self.send_error_response(500, f"获取序列列表失败: {e}")
    
    def handle_images(self, series_uid: str):
        """处理图像列表请求"""
        try:
            print(f"📋 获取序列 {series_uid} 的图像列表")
            images = processor.get_images(series_uid)
            self.send_json_response(images)
        except Exception as e:
            self.send_error_response(500, f"获取图像列表失败: {e}")
    
    def handle_dicom_image(self, path: str, query_params: dict):
        """处理DICOM图像请求"""
        try:
            # 解析文件路径
            path_parts = path.split('/')[3:]  # 去掉 /api/dicom/image/
            file_path = DATA_PATH / '/'.join(path_parts)
            
            # 获取窗宽窗位参数
            window_center = float(query_params.get('windowCenter', [40])[0])
            window_width = float(query_params.get('windowWidth', [400])[0])
            
            print(f"🎉 处理DICOM图像: {file_path.name}")
            
            # 使用纯PyRadiomics处理
            image_data = processor.get_dicom_image_data(file_path, window_center, window_width)
            
            if image_data:
                self.send_response(200)
                self.send_header('Content-Type', 'text/plain')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(image_data.encode('utf-8'))
            else:
                self.send_error_response(500, "图像处理失败")
                
        except Exception as e:
            print(f"❌ DICOM图像处理失败: {e}")
            self.send_error_response(500, f"图像处理失败: {e}")
    
    def handle_dicom_thumbnail(self, path: str, query_params: dict):
        """处理DICOM缩略图请求"""
        # 缩略图使用相同的处理逻辑
        self.handle_dicom_image(path.replace('/thumbnail/', '/image/'), query_params)
    
    def handle_metadata_stats(self):
        """处理元数据统计请求"""
        try:
            print("📊 获取元数据统计")
            dicom_files = processor.scan_dicom_files()
            
            stats = {
                'totalFiles': len(dicom_files),
                'patients': len(set(f['PatientID'] for f in dicom_files)),
                'studies': len(set(f['StudyInstanceUID'] for f in dicom_files)),
                'series': len(set(f['SeriesInstanceUID'] for f in dicom_files)),
                'modalities': list(set(f['Modality'] for f in dicom_files))
            }
            
            self.send_json_response(stats)
        except Exception as e:
            self.send_error_response(500, f"获取统计信息失败: {e}")
    
    def send_json_response(self, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def send_error_response(self, code: int, message: str):
        """发送错误响应"""
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            'error': message,
            'code': code
        }
        response = json.dumps(error_response, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        pass  # 禁用默认日志，使用我们自己的打印

def run_server(port=8000):
    """运行服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, PurePyRadiomicsHandler)
    
    print(f"🎉 纯PyRadiomics DICOM服务器启动")
    print(f"🌐 服务器地址: http://localhost:{port}")
    print(f"📁 数据路径: {DATA_PATH}")
    print(f"🎯 只使用真正的PyRadiomics库")
    print(f"📋 不使用任何模拟器或基础读取器")
    print("按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
