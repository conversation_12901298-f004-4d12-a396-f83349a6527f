#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyRadiomics安装测试脚本
"""

import sys

def test_python_version():
    """测试Python版本"""
    print("🔍 检查Python版本...")
    major = sys.version_info.major
    minor = sys.version_info.minor
    micro = sys.version_info.micro
    
    print(f"当前Python版本: {major}.{minor}.{micro}")
    
    if major == 3 and 7 <= minor <= 9:
        print("✅ Python版本兼容PyRadiomics")
        return True
    else:
        print("❌ Python版本不兼容PyRadiomics")
        print("PyRadiomics需要Python 3.7-3.9")
        print("您当前使用的是Python 3.13.3，需要安装Python 3.9")
        return False

def test_pyradiomics():
    """测试PyRadiomics安装"""
    print("\n🧪 测试PyRadiomics...")
    
    try:
        import pyradiomics
        print(f"✅ PyRadiomics版本: {pyradiomics.__version__}")
        
        from radiomics import featureextractor
        print("✅ PyRadiomics功能模块可用")
        
        import pydicom
        print(f"✅ PyDicom版本: {pydicom.__version__}")
        
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
        
        from PIL import Image
        print("✅ Pillow可用")
        
        import SimpleITK as sitk
        print(f"✅ SimpleITK版本: {sitk.Version.VersionString()}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 PyRadiomics安装测试")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = test_python_version()
    
    if not python_ok:
        print("\n💡 解决方案:")
        print("1. 下载Python 3.9.18: https://www.python.org/downloads/release/python-3918/")
        print("2. 安装Python 3.9到自定义目录（如C:\\Python39）")
        print("3. 使用Python 3.9安装PyRadiomics:")
        print("   C:\\Python39\\python.exe -m pip install pyradiomics pydicom numpy Pillow SimpleITK")
        print("4. 使用Python 3.9运行服务器:")
        print("   C:\\Python39\\python.exe pure_pyradiomics_server.py")
        return
    
    # 测试PyRadiomics
    pyradiomics_ok = test_pyradiomics()
    
    if pyradiomics_ok:
        print("\n🎉 所有库安装成功！")
        print("现在可以运行纯PyRadiomics服务器:")
        print("python pure_pyradiomics_server.py")
    else:
        print("\n🔧 需要安装PyRadiomics:")
        print("pip install pyradiomics pydicom numpy Pillow SimpleITK")

if __name__ == "__main__":
    main()
