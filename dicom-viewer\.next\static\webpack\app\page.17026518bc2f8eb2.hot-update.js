"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nfunction Home() {\n    var _seriesImages_images_currentImageIndex_pixel_spacing;\n    _s();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studies, setStudies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedStudy, setSelectedStudy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [series, setSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSeries, setSelectedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [seriesImages, setSeriesImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [windowCenter, setWindowCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageScale, setImageScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [imagePosition, setImagePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageViewer, setShowImageViewer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE = 'http://localhost:8000';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchPatients();\n            fetchStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const fetchPatients = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients\"));\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const data = await response.json();\n            setPatients(data.patients);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/metadata/stats\"));\n            if (!response.ok) throw new Error('Failed to fetch stats');\n            const data = await response.json();\n            setStats(data);\n        } catch (err) {\n            console.error('Failed to fetch stats:', err);\n        }\n    };\n    const fetchStudies = async (subjectId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients/\").concat(subjectId, \"/studies\"));\n            if (!response.ok) throw new Error('Failed to fetch studies');\n            const data = await response.json();\n            setStudies(data.studies);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeries = async (studyUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/studies/\").concat(studyUid, \"/series\"));\n            if (!response.ok) throw new Error('Failed to fetch series');\n            const data = await response.json();\n            setSeries(data.series);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeriesImages = async (seriesUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/series/\").concat(seriesUid, \"/images\"));\n            if (!response.ok) throw new Error('Failed to fetch series images');\n            const data = await response.json();\n            setSeriesImages(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const handlePatientSelect = (patient)=>{\n        setSelectedPatient(patient);\n        setSelectedStudy(null);\n        setSeries([]);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchStudies(patient.subject_id);\n    };\n    const handleStudySelect = (study)=>{\n        setSelectedStudy(study);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchSeries(study.study_uid);\n    };\n    const handleSeriesSelect = (series)=>{\n        setSelectedSeries(series);\n        setShowImageViewer(true);\n        fetchSeriesImages(series.series_uid);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"正在加载DICOM数据...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl text-red-600\",\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"DICOM 影像查看器\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"患者: \",\n                                                    stats.total_patients\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"研究: \",\n                                                    stats.total_studies\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"序列: \",\n                                                    stats.total_series\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/analysis\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                        children: \"数据分析\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"患者列表 (\",\n                                            patients.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.subject_id) === patient.subject_id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handlePatientSelect(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: patient.subject_id\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            patient.study_count,\n                                                            \" 个研究, \",\n                                                            patient.series_count,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: patient.modalities.join(', ')\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, patient.subject_id, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"研究列表 \",\n                                            selectedPatient && \"(\".concat(selectedPatient.subject_id, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedPatient ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: studies.map((study)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedStudy === null || selectedStudy === void 0 ? void 0 : selectedStudy.study_uid) === study.study_uid ? 'bg-green-100 border-green-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleStudySelect(study),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: study.study_date\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: study.study_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            study.series_count,\n                                                            \" 个序列 - \",\n                                                            study.modalities.join(', ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, study.study_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择患者以查看研究\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"序列列表 \",\n                                            selectedStudy && \"(\".concat(selectedStudy.study_date, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedStudy ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: series.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedSeries === null || selectedSeries === void 0 ? void 0 : selectedSeries.series_uid) === s.series_uid ? 'bg-purple-100 border-purple-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleSeriesSelect(s),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: s.modality\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: s.series_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            s.number_of_images,\n                                                            \" 张图像 - \",\n                                                            s.file_size\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: s.manufacturer\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, s.series_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择研究以查看序列\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"数据集统计\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"成像模态\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.modalities).map((param)=>{\n                                                    let [modality, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: modality\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, modality, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"设备制造商\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.manufacturers).slice(0, 5).map((param)=>{\n                                                    let [manufacturer, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: manufacturer\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, manufacturer, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"起始: \",\n                                                            stats.date_range.earliest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"结束: \",\n                                                            stats.date_range.latest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_patients,\n                                                            \" 名患者\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_studies,\n                                                            \" 个研究\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_series,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this),\n                    showImageViewer && selectedSeries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center p-4 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                selectedSeries.modality,\n                                                \" - \",\n                                                selectedSeries.series_description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageViewer(false),\n                                            className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 max-h-[80vh] overflow-y-auto\",\n                                    children: seriesImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"序列信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-3 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"成像模态:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.modality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"图像总数:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.total_images\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"序列描述:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.description\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"设备制造商:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.manufacturer\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"图像查看器\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    seriesImages.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-100 p-3 rounded flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setImageScale(Math.max(0.1, imageScale - 0.1)),\n                                                                                        className: \"px-2 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                        children: \"缩小\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 409,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: [\n                                                                                            Math.round(imageScale * 100),\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 415,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setImageScale(Math.min(5, imageScale + 0.1)),\n                                                                                        className: \"px-2 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                        children: \"放大\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 418,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setImageScale(1);\n                                                                                    setImagePosition({\n                                                                                        x: 0,\n                                                                                        y: 0\n                                                                                    });\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                                                                                children: \"重置视图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"拖拽图像可平移 | 滚轮可缩放\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMImageViewer, {\n                                                                    image: seriesImages.images[currentImageIndex],\n                                                                    apiBase: API_BASE,\n                                                                    windowCenter: windowCenter,\n                                                                    windowWidth: windowWidth,\n                                                                    scale: imageScale,\n                                                                    position: imagePosition,\n                                                                    onScaleChange: setImageScale,\n                                                                    onPositionChange: setImagePosition\n                                                                }, void 0, false, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            seriesImages.images[currentImageIndex].is_dicom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-4 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-700 mb-3\",\n                                                                        children: \"窗宽窗位调节\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                        children: \"窗位 (Window Center)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 460,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"range\",\n                                                                                        min: \"-1000\",\n                                                                                        max: \"1000\",\n                                                                                        step: \"10\",\n                                                                                        value: windowCenter || seriesImages.images[currentImageIndex].window_center || 40,\n                                                                                        onChange: (e)=>setWindowCenter(Number(e.target.value)),\n                                                                                        className: \"w-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"当前值: \",\n                                                                                            windowCenter || seriesImages.images[currentImageIndex].window_center || 40\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 472,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                        children: \"窗宽 (Window Width)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 477,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"range\",\n                                                                                        min: \"1\",\n                                                                                        max: \"2000\",\n                                                                                        step: \"10\",\n                                                                                        value: windowWidth || seriesImages.images[currentImageIndex].window_width || 400,\n                                                                                        onChange: (e)=>setWindowWidth(Number(e.target.value)),\n                                                                                        className: \"w-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 480,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"当前值: \",\n                                                                                            windowWidth || seriesImages.images[currentImageIndex].window_width || 400\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 489,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3 flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(40);\n                                                                                    setWindowWidth(400);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"软组织\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(300);\n                                                                                    setWindowWidth(1500);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"骨窗\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(-600);\n                                                                                    setWindowWidth(1600);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"肺窗\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(null);\n                                                                                    setWindowWidth(null);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                                                                                children: \"重置\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.max(0, currentImageIndex - 1)),\n                                                                        disabled: currentImageIndex === 0,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"上一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            currentImageIndex + 1,\n                                                                            \" / \",\n                                                                            seriesImages.images.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.min(seriesImages.images.length - 1, currentImageIndex + 1)),\n                                                                        disabled: currentImageIndex === seriesImages.images.length - 1,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"下一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-3 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"文件名: \",\n                                                                                seriesImages.images[currentImageIndex].file_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"大小: \",\n                                                                                (seriesImages.images[currentImageIndex].file_size / 1024).toFixed(1),\n                                                                                \" KB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        seriesImages.images[currentImageIndex].is_dicom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"实例号: \",\n                                                                                        seriesImages.images[currentImageIndex].instance_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"图像尺寸: \",\n                                                                                        seriesImages.images[currentImageIndex].rows,\n                                                                                        \" \\xd7 \",\n                                                                                        seriesImages.images[currentImageIndex].columns\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"像素间距: \",\n                                                                                        (_seriesImages_images_currentImageIndex_pixel_spacing = seriesImages.images[currentImageIndex].pixel_spacing) === null || _seriesImages_images_currentImageIndex_pixel_spacing === void 0 ? void 0 : _seriesImages_images_currentImageIndex_pixel_spacing.join(' × '),\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"层厚: \",\n                                                                                        seriesImages.images[currentImageIndex].slice_thickness,\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                                        children: \"缩略图导航\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                                                        children: seriesImages.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 cursor-pointer border-2 rounded \".concat(index === currentImageIndex ? 'border-blue-500' : 'border-gray-300'),\n                                                                                onClick: ()=>setCurrentImageIndex(index),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMThumbnail, {\n                                                                                    image: image,\n                                                                                    apiBase: API_BASE,\n                                                                                    size: 64\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, image.image_index, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg\",\n                                            children: \"正在加载图像...\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"JH/+lAfNfmYf6VxJ7qSdxybadk0=\");\n_c = Home;\n// DICOM图像查看器组件\nfunction DICOMImageViewer(param) {\n    let { image, apiBase, windowCenter, windowWidth, scale = 1, position = {\n        x: 0,\n        y: 0\n    }, onScaleChange, onPositionChange } = param;\n    _s1();\n    const [imageData, setImageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMImageViewer.useEffect\": ()=>{\n            if (image.is_dicom && image.image_url) {\n                setLoading(true);\n                setError(null);\n                // 构建带窗宽窗位参数的URL\n                let url = \"\".concat(apiBase).concat(image.image_url);\n                const params = new URLSearchParams();\n                if (windowCenter !== null && windowCenter !== undefined) {\n                    params.append('wc', windowCenter.toString());\n                }\n                if (windowWidth !== null && windowWidth !== undefined) {\n                    params.append('ww', windowWidth.toString());\n                }\n                if (params.toString()) {\n                    url += \"?\".concat(params.toString());\n                }\n                fetch(url).then({\n                    \"DICOMImageViewer.useEffect\": (response)=>response.json()\n                }[\"DICOMImageViewer.useEffect\"]).then({\n                    \"DICOMImageViewer.useEffect\": (data)=>{\n                        if (data.success && data.image_data) {\n                            setImageData(data.image_data);\n                        } else {\n                            setError(data.error || '无法加载图像');\n                        }\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).catch({\n                    \"DICOMImageViewer.useEffect\": (err)=>{\n                        setError('加载图像时出错');\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).finally({\n                    \"DICOMImageViewer.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMImageViewer.useEffect\"]);\n            } else {\n                setLoading(false);\n                setError('非DICOM文件或无图像数据');\n            }\n        }\n    }[\"DICOMImageViewer.useEffect\"], [\n        image,\n        apiBase,\n        windowCenter,\n        windowWidth\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"正在加载图像...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 680,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 689,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 688,\n            columnNumber: 7\n        }, this);\n    }\n    if (imageData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: imageData,\n                alt: \"DICOM图像 \".concat(image.file_name),\n                className: \"max-w-full max-h-96 object-contain\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 697,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 696,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-96 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"无图像数据\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 708,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 707,\n        columnNumber: 5\n    }, this);\n}\n_s1(DICOMImageViewer, \"KEnvJThExdJ31O6VITgjvSJA/AQ=\");\n_c1 = DICOMImageViewer;\n// DICOM缩略图组件\nfunction DICOMThumbnail(param) {\n    let { image, apiBase, size = 64 } = param;\n    _s2();\n    const [thumbnailData, setThumbnailData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMThumbnail.useEffect\": ()=>{\n            if (image.is_dicom && image.thumbnail_url) {\n                fetch(\"\".concat(apiBase).concat(image.thumbnail_url)).then({\n                    \"DICOMThumbnail.useEffect\": (response)=>response.json()\n                }[\"DICOMThumbnail.useEffect\"]).then({\n                    \"DICOMThumbnail.useEffect\": (data)=>{\n                        if (data.success && data.thumbnail_data) {\n                            setThumbnailData(data.thumbnail_data);\n                        }\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).catch({\n                    \"DICOMThumbnail.useEffect\": (err)=>{\n                        console.error('加载缩略图时出错:', err);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).finally({\n                    \"DICOMThumbnail.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]);\n            } else {\n                setLoading(false);\n            }\n        }\n    }[\"DICOMThumbnail.useEffect\"], [\n        image,\n        apiBase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n            style: {\n                width: size,\n                height: size\n            },\n            children: \"加载中...\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 740,\n            columnNumber: 7\n        }, this);\n    }\n    if (thumbnailData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: thumbnailData,\n            alt: \"缩略图 \".concat(image.file_name),\n            style: {\n                width: size,\n                height: size\n            },\n            className: \"object-cover\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 751,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n        style: {\n            width: size,\n            height: size\n        },\n        children: image.image_index + 1\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 761,\n        columnNumber: 5\n    }, this);\n}\n_s2(DICOMThumbnail, \"OFgfWwmGhuZAeKYBmxFIBQq2c+4=\");\n_c2 = DICOMThumbnail;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"DICOMImageViewer\");\n$RefreshReg$(_c2, \"DICOMThumbnail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});