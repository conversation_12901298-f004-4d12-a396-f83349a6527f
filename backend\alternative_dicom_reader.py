#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
from pathlib import Path
from typing import Dict, Optional
import struct

# 尝试导入可用的库
try:
    import numpy as np
    NUMPY_AVAILABLE = True
    print("✅ NumPy 可用")
except ImportError:
    NUMPY_AVAILABLE = False
    print("⚠️ NumPy 不可用")

try:
    import pydicom
    PYDICOM_AVAILABLE = True
    print("✅ PyDicom 可用")
except ImportError:
    PYDICOM_AVAILABLE = False
    print("⚠️ PyDicom 不可用")

try:
    import SimpleITK as sitk
    SIMPLEITK_AVAILABLE = True
    print("✅ SimpleITK 可用")
except ImportError:
    SIMPLEITK_AVAILABLE = False
    print("⚠️ SimpleITK 不可用")

try:
    from PIL import Image
    PIL_AVAILABLE = True
    print("✅ Pillow 可用")
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ Pillow 不可用")

# 检查可用性
ALTERNATIVE_READER_AVAILABLE = NUMPY_AVAILABLE and (PYDICOM_AVAILABLE or SIMPLEITK_AVAILABLE) and PIL_AVAILABLE

class AlternativeDicomReader:
    """替代的DICOM读取器，使用可用的库"""
    
    def __init__(self, path: str):
        self.path = Path(path)
        self._dataset = None
        self._image = None
        self._array = None
        
    def read_dicom(self) -> Dict:
        """读取DICOM文件"""
        if not ALTERNATIVE_READER_AVAILABLE:
            raise ImportError("替代DICOM读取器所需的库不可用")
            
        try:
            print(f"🔧 使用替代DICOM读取器: {self.path.name}")
            
            # 方法1: 优先使用SimpleITK
            if SIMPLEITK_AVAILABLE:
                return self._read_with_simpleitk()
            
            # 方法2: 使用PyDicom + NumPy
            elif PYDICOM_AVAILABLE and NUMPY_AVAILABLE:
                return self._read_with_pydicom()
            
            else:
                raise ImportError("没有可用的DICOM读取方法")
                
        except Exception as e:
            print(f"❌ 替代DICOM读取失败: {e}")
            raise
    
    def _read_with_simpleitk(self) -> Dict:
        """使用SimpleITK读取"""
        print("📖 使用SimpleITK读取DICOM")
        
        # 读取图像
        self._image = sitk.ReadImage(str(self.path))
        self._array = sitk.GetArrayFromImage(self._image)
        
        # 获取统计信息
        pixel_min = float(np.min(self._array))
        pixel_max = float(np.max(self._array))
        pixel_mean = float(np.mean(self._array))
        
        print(f"✅ SimpleITK读取成功: {self._array.shape}, 范围: {pixel_min:.0f}-{pixel_max:.0f}")
        
        return {
            'success': True,
            'reader': 'SimpleITK',
            'image_shape': self._array.shape,
            'pixel_range': [pixel_min, pixel_max],
            'pixel_mean': pixel_mean,
            'spacing': self._image.GetSpacing(),
            'origin': self._image.GetOrigin(),
            'direction': self._image.GetDirection()
        }
    
    def _read_with_pydicom(self) -> Dict:
        """使用PyDicom读取"""
        print("📖 使用PyDicom读取DICOM")
        
        # 读取DICOM文件
        self._dataset = pydicom.dcmread(str(self.path))
        
        if not hasattr(self._dataset, 'pixel_array'):
            raise ValueError("DICOM文件没有像素数据")
        
        # 获取像素数组
        self._array = self._dataset.pixel_array
        
        # 处理多维数组
        if len(self._array.shape) == 3:
            # 如果是3D，取中间切片
            self._array = self._array[self._array.shape[0] // 2]
        
        # 获取统计信息
        pixel_min = float(np.min(self._array))
        pixel_max = float(np.max(self._array))
        pixel_mean = float(np.mean(self._array))
        
        print(f"✅ PyDicom读取成功: {self._array.shape}, 范围: {pixel_min:.0f}-{pixel_max:.0f}")
        
        # 获取元数据
        metadata = {
            'PatientID': getattr(self._dataset, 'PatientID', 'Unknown'),
            'StudyInstanceUID': getattr(self._dataset, 'StudyInstanceUID', 'Unknown'),
            'Rows': getattr(self._dataset, 'Rows', 0),
            'Columns': getattr(self._dataset, 'Columns', 0),
            'WindowCenter': getattr(self._dataset, 'WindowCenter', None),
            'WindowWidth': getattr(self._dataset, 'WindowWidth', None),
        }
        
        return {
            'success': True,
            'reader': 'PyDicom',
            'image_shape': self._array.shape,
            'pixel_range': [pixel_min, pixel_max],
            'pixel_mean': pixel_mean,
            'metadata': metadata
        }
    
    def get_pixel_array(self):
        """获取像素数组"""
        if self._array is None:
            self.read_dicom()
        return self._array
    
    def create_image_base64(self, window_center: float = None, window_width: float = None) -> str:
        """创建Base64编码的图像"""
        try:
            if self._array is None:
                self.read_dicom()
            
            # 确保是2D数组
            image_2d = self._array
            if len(image_2d.shape) > 2:
                image_2d = image_2d[:, :, 0] if image_2d.shape[2] > 0 else image_2d.squeeze()
            
            print(f"🎨 创建图像: 形状={image_2d.shape}")
            
            # 应用窗宽窗位
            if window_center is not None and window_width is not None:
                print(f"🎨 应用窗宽窗位: WC={window_center}, WW={window_width}")
                min_val = window_center - window_width / 2
                max_val = window_center + window_width / 2
                image_2d = np.clip(image_2d, min_val, max_val)
                image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
            else:
                # 自动缩放到0-255
                min_val = np.min(image_2d)
                max_val = np.max(image_2d)
                if max_val > min_val:
                    image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
                else:
                    image_2d = np.zeros_like(image_2d, dtype=np.uint8)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_2d, mode='L')
            
            # 转换为Base64
            import io
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            print(f"✅ 图像创建成功: {len(image_base64)} 字符")
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            print(f"❌ 图像创建失败: {e}")
            raise

def read_dicom_with_alternative_reader(file_path: str, window_center: float = None, window_width: float = None) -> str:
    """使用替代读取器读取DICOM并返回Base64图像"""
    try:
        print(f"🔧 启动替代DICOM读取器")
        
        if not ALTERNATIVE_READER_AVAILABLE:
            raise ImportError("替代DICOM读取器所需的库不可用")
        
        reader = AlternativeDicomReader(file_path)
        info = reader.read_dicom()
        
        print(f"✅ 替代读取器读取成功:")
        print(f"   - 读取器: {info['reader']}")
        print(f"   - 图像形状: {info['image_shape']}")
        print(f"   - 像素范围: {info['pixel_range']}")
        print(f"   - 像素平均值: {info['pixel_mean']:.2f}")
        
        # 创建图像
        image_base64 = reader.create_image_base64(window_center, window_width)
        
        return image_base64
        
    except Exception as e:
        print(f"❌ 替代读取器处理失败: {e}")
        raise

if __name__ == "__main__":
    # 测试代码
    print("🔧 替代DICOM读取器状态:")
    print(f"   - NumPy: {'✅' if NUMPY_AVAILABLE else '❌'}")
    print(f"   - PyDicom: {'✅' if PYDICOM_AVAILABLE else '❌'}")
    print(f"   - SimpleITK: {'✅' if SIMPLEITK_AVAILABLE else '❌'}")
    print(f"   - Pillow: {'✅' if PIL_AVAILABLE else '❌'}")
    print(f"   - 整体可用性: {'✅' if ALTERNATIVE_READER_AVAILABLE else '❌'}")
