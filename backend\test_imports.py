#!/usr/bin/env python3
"""测试DICOM相关库的导入"""

import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

# 测试基础库
try:
    import os
    print("✅ os - 成功")
except ImportError as e:
    print(f"❌ os - 失败: {e}")

try:
    import json
    print("✅ json - 成功")
except ImportError as e:
    print(f"❌ json - 失败: {e}")

try:
    import pathlib
    print("✅ pathlib - 成功")
except ImportError as e:
    print(f"❌ pathlib - 失败: {e}")

# 测试DICOM相关库
try:
    import pydicom
    print(f"✅ pydicom - 成功 (版本: {pydicom.__version__})")
except ImportError as e:
    print(f"❌ pydicom - 失败: {e}")

try:
    from PIL import Image
    print(f"✅ PIL (Pillow) - 成功")
except ImportError as e:
    print(f"❌ PIL (Pillow) - 失败: {e}")

try:
    import numpy as np
    print(f"✅ numpy - 成功 (版本: {np.__version__})")
except ImportError as e:
    print(f"❌ numpy - 失败: {e}")

# 测试DICOM文件读取
print("\n=== 测试DICOM文件读取 ===")
try:
    from pathlib import Path
    
    # 查找第一个DICOM文件
    data_path = Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics")
    if data_path.exists():
        print(f"数据目录存在: {data_path}")
        
        # 查找第一个患者目录
        patient_dirs = [d for d in data_path.iterdir() if d.is_dir()]
        if patient_dirs:
            first_patient = patient_dirs[0]
            print(f"第一个患者目录: {first_patient.name}")
            
            # 查找第一个研究目录
            study_dirs = [d for d in first_patient.iterdir() if d.is_dir()]
            if study_dirs:
                first_study = study_dirs[0]
                print(f"第一个研究目录: {first_study.name}")
                
                # 查找第一个序列目录
                series_dirs = [d for d in first_study.iterdir() if d.is_dir()]
                if series_dirs:
                    first_series = series_dirs[0]
                    print(f"第一个序列目录: {first_series.name}")
                    
                    # 查找DICOM文件
                    dicom_files = [f for f in first_series.iterdir() if f.is_file() and not f.name.startswith('.')]
                    if dicom_files:
                        first_dicom = dicom_files[0]
                        print(f"第一个DICOM文件: {first_dicom.name}")
                        
                        # 尝试读取DICOM文件
                        try:
                            import pydicom
                            ds = pydicom.dcmread(str(first_dicom))
                            print(f"✅ DICOM文件读取成功!")
                            print(f"   患者ID: {getattr(ds, 'PatientID', 'Unknown')}")
                            print(f"   模态: {getattr(ds, 'Modality', 'Unknown')}")
                            print(f"   图像尺寸: {getattr(ds, 'Rows', 'Unknown')} x {getattr(ds, 'Columns', 'Unknown')}")
                            print(f"   是否有像素数据: {hasattr(ds, 'pixel_array')}")
                            
                            if hasattr(ds, 'pixel_array'):
                                pixel_array = ds.pixel_array
                                print(f"   像素数组形状: {pixel_array.shape}")
                                print(f"   像素数据类型: {pixel_array.dtype}")
                                
                        except Exception as e:
                            print(f"❌ DICOM文件读取失败: {e}")
                    else:
                        print("❌ 未找到DICOM文件")
                else:
                    print("❌ 未找到序列目录")
            else:
                print("❌ 未找到研究目录")
        else:
            print("❌ 未找到患者目录")
    else:
        print(f"❌ 数据目录不存在: {data_path}")
        
except Exception as e:
    print(f"❌ 测试DICOM文件读取时出错: {e}")

print("\n=== 测试完成 ===")
