#!/usr/bin/env python3
"""测试图像生成功能"""

from pathlib import Path
from dicom_processor import DICOMProcessor

def test_image_generation():
    """测试图像生成"""
    print("=== 测试DICOM图像生成 ===")
    
    # 初始化处理器
    data_path = Path("../sourcedata/a/manifest-1603198545583")
    processor = DICOMProcessor(data_path)
    
    # 查找第一个DICOM文件
    dicom_path = data_path / "NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-001.dcm"
    
    if dicom_path.exists():
        print(f"✅ 找到DICOM文件: {dicom_path.name}")
        
        # 测试读取文件信息
        dicom_info = processor.read_dicom_file(dicom_path)
        if dicom_info:
            print(f"✅ 读取文件信息成功:")
            for key, value in dicom_info.items():
                print(f"   {key}: {value}")
        
        # 测试图像生成
        print("\n--- 测试图像生成 ---")
        image_data = processor.get_dicom_image_data(dicom_path)
        if image_data:
            print(f"✅ 生成图像成功: {len(image_data)} 字符")
            print(f"   图像类型: {image_data[:50]}...")
        else:
            print("❌ 图像生成失败")
        
        # 测试缩略图生成
        print("\n--- 测试缩略图生成 ---")
        thumbnail_data = processor.get_dicom_thumbnail(dicom_path)
        if thumbnail_data:
            print(f"✅ 生成缩略图成功: {len(thumbnail_data)} 字符")
            print(f"   缩略图类型: {thumbnail_data[:50]}...")
        else:
            print("❌ 缩略图生成失败")
        
        # 测试窗宽窗位
        print("\n--- 测试窗宽窗位调节 ---")
        image_soft = processor.get_dicom_image_data(dicom_path, window_center=40, window_width=400)
        image_bone = processor.get_dicom_image_data(dicom_path, window_center=300, window_width=1500)
        image_lung = processor.get_dicom_image_data(dicom_path, window_center=-600, window_width=1600)
        
        if image_soft and image_bone and image_lung:
            print("✅ 窗宽窗位调节功能正常")
            print(f"   软组织窗: {len(image_soft)} 字符")
            print(f"   骨窗: {len(image_bone)} 字符") 
            print(f"   肺窗: {len(image_lung)} 字符")
        else:
            print("❌ 窗宽窗位调节失败")
    
    else:
        print(f"❌ 未找到DICOM文件: {dicom_path}")

if __name__ == "__main__":
    test_image_generation()
