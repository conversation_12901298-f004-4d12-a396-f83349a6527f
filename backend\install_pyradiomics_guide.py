#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyRadiomics安装指南 - Python 3.9环境
"""

import sys
import subprocess
import os

def check_current_python():
    """检查当前Python版本"""
    print("🔍 当前Python环境检查:")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    major = sys.version_info.major
    minor = sys.version_info.minor
    
    print(f"\n版本详情: Python {major}.{minor}.{sys.version_info.micro}")
    
    if major == 3 and 7 <= minor <= 9:
        print("✅ 当前Python版本与PyRadiomics兼容")
        return True
    else:
        print("❌ 当前Python版本与PyRadiomics不兼容")
        print("   PyRadiomics需要Python 3.7-3.9")
        return False

def check_conda():
    """检查conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Conda可用: {result.stdout.strip()}")
            return True
    except:
        pass
    
    print("❌ Conda不可用")
    return False

def check_pyenv():
    """检查pyenv是否可用"""
    try:
        result = subprocess.run(['pyenv', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Pyenv可用: {result.stdout.strip()}")
            return True
    except:
        pass
    
    print("❌ Pyenv不可用")
    return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 PyRadiomics安装解决方案:")
    
    compatible = check_current_python()
    
    if compatible:
        print("\n🎉 可以直接安装PyRadiomics:")
        print("pip install pyradiomics numpy SimpleITK pydicom Pillow")
        return
    
    print("\n📋 需要Python 3.9环境，以下是解决方案:")
    
    has_conda = check_conda()
    has_pyenv = check_pyenv()
    
    if has_conda:
        print("\n🔧 方案1: 使用Conda创建Python 3.9环境")
        print("conda create -n pyradiomics python=3.9")
        print("conda activate pyradiomics")
        print("pip install pyradiomics numpy SimpleITK pydicom Pillow")
        
    elif has_pyenv:
        print("\n🔧 方案2: 使用Pyenv安装Python 3.9")
        print("pyenv install 3.9.18")
        print("pyenv local 3.9.18")
        print("pip install pyradiomics numpy SimpleITK pydicom Pillow")
        
    else:
        print("\n🔧 方案3: 手动安装Python 3.9")
        print("1. 下载Python 3.9.18: https://www.python.org/downloads/release/python-3918/")
        print("2. 安装Python 3.9.18")
        print("3. 使用Python 3.9运行项目")
        print("4. pip install pyradiomics numpy SimpleITK pydicom Pillow")
        
        print("\n🔧 方案4: 使用虚拟环境")
        print("# 如果系统有多个Python版本")
        print("python3.9 -m venv pyradiomics_env")
        print("# Windows:")
        print("pyradiomics_env\\Scripts\\activate")
        print("# Linux/Mac:")
        print("source pyradiomics_env/bin/activate")
        print("pip install pyradiomics numpy SimpleITK pydicom Pillow")

def test_pyradiomics_installation():
    """测试PyRadiomics是否已安装"""
    print("\n🧪 测试PyRadiomics安装:")
    try:
        import pyradiomics
        print(f"✅ PyRadiomics已安装: 版本 {pyradiomics.__version__}")
        
        # 测试基本功能
        from radiomics import featureextractor
        print("✅ PyRadiomics功能模块可用")
        return True
        
    except ImportError as e:
        print(f"❌ PyRadiomics未安装: {e}")
        return False

def main():
    print("🎯 PyRadiomics安装指南")
    print("=" * 50)
    
    # 检查当前安装状态
    if test_pyradiomics_installation():
        print("\n🎉 PyRadiomics已经可用！无需额外安装。")
        return
    
    # 提供安装解决方案
    provide_solutions()
    
    print("\n📝 安装完成后的验证:")
    print("python -c \"import pyradiomics; print('PyRadiomics版本:', pyradiomics.__version__)\"")
    
    print("\n🔄 安装完成后，重启DICOM服务器:")
    print("cd backend && python basic_server.py")

if __name__ == "__main__":
    main()
