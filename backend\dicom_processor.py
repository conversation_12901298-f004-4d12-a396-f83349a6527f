import os
import base64
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple

try:
    import pydicom
    from PIL import Image
    import numpy as np
    DICOM_AVAILABLE = True
    print("✅ DICOM libraries loaded successfully!")
except ImportError as e:
    DICOM_AVAILABLE = False
    print(f"⚠️ Warning: DICOM libraries not available ({e}). Using fallback mode.")

class DICOMProcessor:
    def __init__(self, data_path: Path):
        self.data_path = data_path
        self.dicom_available = DICOM_AVAILABLE

    def read_dicom_file(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM文件并返回基本信息"""
        if not self.dicom_available:
            return None

        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(str(file_path))

            # 提取基本信息
            info = {
                'patient_id': getattr(ds, 'PatientID', 'Unknown'),
                'patient_name': str(getattr(ds, 'PatientName', 'Unknown')),
                'study_date': getattr(ds, 'StudyDate', 'Unknown'),
                'study_time': getattr(ds, 'StudyTime', 'Unknown'),
                'modality': getattr(ds, 'Modality', 'Unknown'),
                'series_description': getattr(ds, 'SeriesDescription', 'Unknown'),
                'instance_number': getattr(ds, 'InstanceNumber', 0),
                'rows': getattr(ds, 'Rows', 0),
                'columns': getattr(ds, 'Columns', 0),
                'pixel_spacing': getattr(ds, 'PixelSpacing', [1.0, 1.0]),
                'slice_thickness': getattr(ds, 'SliceThickness', 1.0),
                'window_center': getattr(ds, 'WindowCenter', None),
                'window_width': getattr(ds, 'WindowWidth', None),
                'file_size': file_path.stat().st_size,
                'has_pixel_data': hasattr(ds, 'pixel_array')
            }

            return info

        except Exception as e:
            print(f"Error reading DICOM file {file_path}: {e}")
            return None

    def get_dicom_image_data(self, file_path: Path, window_center: Optional[float] = None,
                           window_width: Optional[float] = None) -> Optional[str]:
        """读取DICOM图像数据并转换为base64编码的PNG"""
        if not self.dicom_available:
            return self._create_placeholder_image(file_path.name)

        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(str(file_path))

            if not hasattr(ds, 'pixel_array'):
                return None

            # 获取像素数据
            pixel_array = ds.pixel_array

            # 处理不同的像素数据类型
            if len(pixel_array.shape) == 3:
                # 彩色图像
                if pixel_array.shape[2] == 3:
                    # RGB图像
                    image_array = pixel_array
                else:
                    # 取第一个通道
                    image_array = pixel_array[:, :, 0]
            else:
                # 灰度图像
                image_array = pixel_array

            # 窗宽窗位调节
            if window_center is None:
                window_center = getattr(ds, 'WindowCenter', None)
            if window_width is None:
                window_width = getattr(ds, 'WindowWidth', None)

            if window_center is not None and window_width is not None:
                # 如果是列表，取第一个值
                if isinstance(window_center, (list, tuple)):
                    window_center = window_center[0]
                if isinstance(window_width, (list, tuple)):
                    window_width = window_width[0]

                # 应用窗宽窗位
                img_min = window_center - window_width // 2
                img_max = window_center + window_width // 2
                image_array = np.clip(image_array, img_min, img_max)
                image_array = ((image_array - img_min) / (img_max - img_min) * 255).astype(np.uint8)
            else:
                # 自动调节对比度
                img_min = np.min(image_array)
                img_max = np.max(image_array)
                if img_max > img_min:
                    image_array = ((image_array - img_min) / (img_max - img_min) * 255).astype(np.uint8)
                else:
                    image_array = np.zeros_like(image_array, dtype=np.uint8)

            # 转换为PIL图像
            if len(image_array.shape) == 3:
                pil_image = Image.fromarray(image_array, 'RGB')
            else:
                pil_image = Image.fromarray(image_array, 'L')

            # 转换为base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"

        except Exception as e:
            print(f"Error processing DICOM image {file_path}: {e}")
            return None

    def get_series_dicom_files(self, series_path: Path) -> List[Dict]:
        """获取序列中的所有DICOM文件信息"""
        dicom_files = []

        if not series_path.exists() or not series_path.is_dir():
            return dicom_files

        # 查找所有可能的DICOM文件
        for file_path in series_path.iterdir():
            if file_path.is_file() and not file_path.name.startswith('.'):
                if self.dicom_available:
                    # 尝试读取DICOM信息
                    dicom_info = self.read_dicom_file(file_path)
                    if dicom_info:
                        dicom_info['file_path'] = str(file_path.relative_to(self.data_path))
                        dicom_info['file_name'] = file_path.name
                        dicom_files.append(dicom_info)
                        continue

                # Fallback: 添加基本文件信息
                dicom_files.append({
                    'file_path': str(file_path.relative_to(self.data_path)),
                    'file_name': file_path.name,
                    'file_size': file_path.stat().st_size,
                    'is_dicom': self._is_likely_dicom(file_path),
                    'instance_number': self._extract_instance_from_filename(file_path.name)
                })

        # 按实例号排序
        dicom_files.sort(key=lambda x: x.get('instance_number', 0))

        return dicom_files

    def _is_likely_dicom(self, file_path: Path) -> bool:
        """简单判断文件是否可能是DICOM文件"""
        # 检查文件扩展名
        ext = file_path.suffix.lower()
        if ext in ['.dcm', '.dicom']:
            return True

        # 检查文件是否没有扩展名（很多DICOM文件没有扩展名）
        if not ext:
            return True

        # 检查文件大小（DICOM文件通常比较大）
        try:
            size = file_path.stat().st_size
            if size > 1024:  # 大于1KB
                return True
        except:
            pass

        return False

    def _extract_instance_from_filename(self, filename: str) -> int:
        """从文件名中提取实例号"""
        # 尝试从文件名中提取数字
        import re
        numbers = re.findall(r'\d+', filename)
        if numbers:
            # 返回最后一个数字作为实例号
            return int(numbers[-1])
        return 0

    def get_dicom_thumbnail(self, file_path: Path, size: Tuple[int, int] = (128, 128)) -> Optional[str]:
        """生成DICOM图像缩略图"""
        if not self.dicom_available:
            return self._create_placeholder_thumbnail(file_path.name, size)

        try:
            # 获取原始图像数据
            image_data = self.get_dicom_image_data(file_path)
            if not image_data:
                return None

            # 解码base64图像
            image_data = image_data.split(',')[1]  # 移除data:image/png;base64,前缀
            image_bytes = base64.b64decode(image_data)

            # 创建PIL图像并生成缩略图
            pil_image = Image.open(io.BytesIO(image_bytes))
            pil_image.thumbnail(size, Image.Resampling.LANCZOS)

            # 转换回base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            thumbnail_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{thumbnail_str}"

        except Exception as e:
            print(f"Error creating thumbnail for {file_path}: {e}")
            return self._create_placeholder_thumbnail(file_path.name, size)

    def _create_placeholder_thumbnail(self, filename: str, size: Tuple[int, int] = (128, 128)) -> str:
        """创建占位符缩略图"""
        import base64

        # 创建简单的SVG占位符
        svg_content = f'''<svg width="{size[0]}" height="{size[1]}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <text x="50%" y="40%" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">DICOM</text>
            <text x="50%" y="60%" text-anchor="middle" font-family="Arial" font-size="10" fill="#999">{filename[:10]}...</text>
        </svg>'''

        # 转换为base64
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')

        return f"data:image/svg+xml;base64,{svg_base64}"

    def _create_placeholder_image(self, filename: str, width: int = 512, height: int = 512) -> str:
        """创建占位符图像"""
        import base64
        import random

        # 创建更真实的医学图像样式占位符
        svg_content = f'''<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="grad1" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:#4a4a4a;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#2a2a2a;stop-opacity:1" />
                </radialGradient>
                <pattern id="noise" patternUnits="userSpaceOnUse" width="4" height="4">
                    <rect width="4" height="4" fill="#333"/>
                    <circle cx="2" cy="2" r="0.5" fill="#444"/>
                </pattern>
            </defs>

            <!-- 背景 -->
            <rect width="100%" height="100%" fill="url(#grad1)"/>

            <!-- 噪声纹理 -->
            <rect width="100%" height="100%" fill="url(#noise)" opacity="0.3"/>

            <!-- 模拟解剖结构 -->
            <ellipse cx="50%" cy="50%" rx="40%" ry="35%" fill="#555" opacity="0.6"/>
            <ellipse cx="45%" cy="45%" rx="20%" ry="25%" fill="#666" opacity="0.4"/>
            <ellipse cx="55%" cy="55%" rx="15%" ry="20%" fill="#777" opacity="0.3"/>

            <!-- 十字线 -->
            <line x1="50%" y1="0" x2="50%" y2="100%" stroke="#888" stroke-width="1" opacity="0.5"/>
            <line x1="0" y1="50%" x2="100%" y2="50%" stroke="#888" stroke-width="1" opacity="0.5"/>

            <!-- 信息文本 -->
            <rect x="10" y="10" width="200" height="80" fill="#000" opacity="0.7" rx="5"/>
            <text x="20" y="30" font-family="monospace" font-size="14" fill="#ccc">DICOM 图像</text>
            <text x="20" y="50" font-family="monospace" font-size="12" fill="#999">{filename[:20]}...</text>
            <text x="20" y="70" font-family="monospace" font-size="10" fill="#777">模拟医学图像</text>

            <!-- 右下角信息 -->
            <rect x="{width-120}" y="{height-60}" width="110" height="50" fill="#000" opacity="0.7" rx="5"/>
            <text x="{width-115}" y="{height-40}" font-family="monospace" font-size="10" fill="#ccc">{width}×{height}</text>
            <text x="{width-115}" y="{height-25}" font-family="monospace" font-size="10" fill="#999">16-bit</text>
            <text x="{width-115}" y="{height-10}" font-family="monospace" font-size="10" fill="#777">WL: 40/400</text>
        </svg>'''

        # 转换为base64
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')

        return f"data:image/svg+xml;base64,{svg_base64}"
