import os
import base64
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple

try:
    import pydicom
    from PIL import Image
    import numpy as np
    DICOM_AVAILABLE = True
except ImportError:
    DICOM_AVAILABLE = False
    print("Warning: pydicom, PIL, or numpy not available. DICOM image processing will be limited.")

class DICOMProcessor:
    def __init__(self, data_path: Path):
        self.data_path = data_path
        self.dicom_available = DICOM_AVAILABLE
    
    def read_dicom_file(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM文件并返回基本信息"""
        if not self.dicom_available:
            return None
        
        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(str(file_path))
            
            # 提取基本信息
            info = {
                'patient_id': getattr(ds, 'PatientID', 'Unknown'),
                'patient_name': str(getattr(ds, 'PatientName', 'Unknown')),
                'study_date': getattr(ds, 'StudyDate', 'Unknown'),
                'study_time': getattr(ds, 'StudyTime', 'Unknown'),
                'modality': getattr(ds, 'Modality', 'Unknown'),
                'series_description': getattr(ds, 'SeriesDescription', 'Unknown'),
                'instance_number': getattr(ds, 'InstanceNumber', 0),
                'rows': getattr(ds, 'Rows', 0),
                'columns': getattr(ds, 'Columns', 0),
                'pixel_spacing': getattr(ds, 'PixelSpacing', [1.0, 1.0]),
                'slice_thickness': getattr(ds, 'SliceThickness', 1.0),
                'window_center': getattr(ds, 'WindowCenter', None),
                'window_width': getattr(ds, 'WindowWidth', None),
                'file_size': file_path.stat().st_size,
                'has_pixel_data': hasattr(ds, 'pixel_array')
            }
            
            return info
            
        except Exception as e:
            print(f"Error reading DICOM file {file_path}: {e}")
            return None
    
    def get_dicom_image_data(self, file_path: Path, window_center: Optional[float] = None, 
                           window_width: Optional[float] = None) -> Optional[str]:
        """读取DICOM图像数据并转换为base64编码的PNG"""
        if not self.dicom_available:
            return None
        
        try:
            # 读取DICOM文件
            ds = pydicom.dcmread(str(file_path))
            
            if not hasattr(ds, 'pixel_array'):
                return None
            
            # 获取像素数据
            pixel_array = ds.pixel_array
            
            # 处理不同的像素数据类型
            if len(pixel_array.shape) == 3:
                # 彩色图像
                if pixel_array.shape[2] == 3:
                    # RGB图像
                    image_array = pixel_array
                else:
                    # 取第一个通道
                    image_array = pixel_array[:, :, 0]
            else:
                # 灰度图像
                image_array = pixel_array
            
            # 窗宽窗位调节
            if window_center is None:
                window_center = getattr(ds, 'WindowCenter', None)
            if window_width is None:
                window_width = getattr(ds, 'WindowWidth', None)
            
            if window_center is not None and window_width is not None:
                # 如果是列表，取第一个值
                if isinstance(window_center, (list, tuple)):
                    window_center = window_center[0]
                if isinstance(window_width, (list, tuple)):
                    window_width = window_width[0]
                
                # 应用窗宽窗位
                img_min = window_center - window_width // 2
                img_max = window_center + window_width // 2
                image_array = np.clip(image_array, img_min, img_max)
                image_array = ((image_array - img_min) / (img_max - img_min) * 255).astype(np.uint8)
            else:
                # 自动调节对比度
                img_min = np.min(image_array)
                img_max = np.max(image_array)
                if img_max > img_min:
                    image_array = ((image_array - img_min) / (img_max - img_min) * 255).astype(np.uint8)
                else:
                    image_array = np.zeros_like(image_array, dtype=np.uint8)
            
            # 转换为PIL图像
            if len(image_array.shape) == 3:
                pil_image = Image.fromarray(image_array, 'RGB')
            else:
                pil_image = Image.fromarray(image_array, 'L')
            
            # 转换为base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            print(f"Error processing DICOM image {file_path}: {e}")
            return None
    
    def get_series_dicom_files(self, series_path: Path) -> List[Dict]:
        """获取序列中的所有DICOM文件信息"""
        dicom_files = []
        
        if not series_path.exists() or not series_path.is_dir():
            return dicom_files
        
        # 查找所有可能的DICOM文件
        for file_path in series_path.iterdir():
            if file_path.is_file() and not file_path.name.startswith('.'):
                # 尝试读取DICOM信息
                dicom_info = self.read_dicom_file(file_path)
                if dicom_info:
                    dicom_info['file_path'] = str(file_path.relative_to(self.data_path))
                    dicom_info['file_name'] = file_path.name
                    dicom_files.append(dicom_info)
                else:
                    # 即使不是DICOM文件，也添加基本信息
                    dicom_files.append({
                        'file_path': str(file_path.relative_to(self.data_path)),
                        'file_name': file_path.name,
                        'file_size': file_path.stat().st_size,
                        'is_dicom': False
                    })
        
        # 按实例号排序
        dicom_files.sort(key=lambda x: x.get('instance_number', 0))
        
        return dicom_files
    
    def get_dicom_thumbnail(self, file_path: Path, size: Tuple[int, int] = (128, 128)) -> Optional[str]:
        """生成DICOM图像缩略图"""
        if not self.dicom_available:
            return None
        
        try:
            # 获取原始图像数据
            image_data = self.get_dicom_image_data(file_path)
            if not image_data:
                return None
            
            # 解码base64图像
            image_data = image_data.split(',')[1]  # 移除data:image/png;base64,前缀
            image_bytes = base64.b64decode(image_data)
            
            # 创建PIL图像并生成缩略图
            pil_image = Image.open(io.BytesIO(image_bytes))
            pil_image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # 转换回base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            thumbnail_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{thumbnail_str}"
            
        except Exception as e:
            print(f"Error creating thumbnail for {file_path}: {e}")
            return None
