#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 测试DICOM处理库安装状态...")

# 测试NumPy
try:
    import numpy as np
    print(f"✅ NumPy {np.__version__} - 安装成功")
    numpy_ok = True
except ImportError as e:
    print(f"❌ NumPy - 安装失败: {e}")
    numpy_ok = False

# 测试PyDicom
try:
    import pydicom
    print(f"✅ PyDicom {pydicom.__version__} - 安装成功")
    pydicom_ok = True
except ImportError as e:
    print(f"❌ PyDicom - 安装失败: {e}")
    pydicom_ok = False

# 测试Pillow
try:
    from PIL import Image
    print(f"✅ Pillow - 安装成功")
    pillow_ok = True
except ImportError as e:
    print(f"❌ Pillow - 安装失败: {e}")
    pillow_ok = False

# 测试SimpleITK
try:
    import SimpleITK as sitk
    print(f"✅ SimpleITK {sitk.Version.VersionString()} - 安装成功")
    sitk_ok = True
except ImportError as e:
    print(f"❌ SimpleITK - 安装失败: {e}")
    sitk_ok = False

print("\n📊 安装状态总结:")
print(f"   NumPy: {'✅' if numpy_ok else '❌'}")
print(f"   PyDicom: {'✅' if pydicom_ok else '❌'}")
print(f"   Pillow: {'✅' if pillow_ok else '❌'}")
print(f"   SimpleITK: {'✅' if sitk_ok else '❌'}")

if numpy_ok and pydicom_ok and pillow_ok:
    print("\n🎉 基础DICOM处理库安装完成！")
    print("✅ 现在可以读取真实的DICOM数据了")
    
    if sitk_ok:
        print("🎯 完整的医学图像处理能力已就绪")
    else:
        print("⚠️ SimpleITK仍在安装中，但基础功能已可用")
else:
    print("\n⚠️ 部分库安装失败，请检查网络连接或手动安装")
