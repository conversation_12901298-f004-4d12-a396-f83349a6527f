'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Patient {
  subject_id: string;
  study_count: number;
  series_count: number;
  modalities: string[];
  study_dates: string[];
}

interface Study {
  study_uid: string;
  study_date: string;
  study_description: string;
  series_count: number;
  modalities: string[];
}

interface Series {
  series_uid: string;
  series_description: string;
  modality: string;
  manufacturer: string;
  sop_class_name: string;
  number_of_images: number;
  file_size: string;
  file_location: string;
}

interface Image {
  image_index: number;
  file_name: string;
  file_path: string;
  file_size: number;
}

interface SeriesImages {
  images: Image[];
  series_uid: string;
  total_images: number;
  series_info: {
    modality: string;
    description: string;
    manufacturer: string;
  };
}

interface Stats {
  total_patients: number;
  total_studies: number;
  total_series: number;
  modalities: Record<string, number>;
  manufacturers: Record<string, number>;
  date_range: {
    earliest: string;
    latest: string;
  };
}

export default function Home() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [studies, setStudies] = useState<Study[]>([]);
  const [selectedStudy, setSelectedStudy] = useState<Study | null>(null);
  const [series, setSeries] = useState<Series[]>([]);
  const [selectedSeries, setSelectedSeries] = useState<Series | null>(null);
  const [seriesImages, setSeriesImages] = useState<SeriesImages | null>(null);
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showImageViewer, setShowImageViewer] = useState(false);

  const API_BASE = 'http://localhost:8000';

  useEffect(() => {
    fetchPatients();
    fetchStats();
  }, []);

  const fetchPatients = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/patients`);
      if (!response.ok) throw new Error('Failed to fetch patients');
      const data = await response.json();
      setPatients(data.patients);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/metadata/stats`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Failed to fetch stats:', err);
    }
  };

  const fetchStudies = async (subjectId: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/patients/${subjectId}/studies`);
      if (!response.ok) throw new Error('Failed to fetch studies');
      const data = await response.json();
      setStudies(data.studies);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchSeries = async (studyUid: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/studies/${studyUid}/series`);
      if (!response.ok) throw new Error('Failed to fetch series');
      const data = await response.json();
      setSeries(data.series);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchSeriesImages = async (seriesUid: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/series/${seriesUid}/images`);
      if (!response.ok) throw new Error('Failed to fetch series images');
      const data = await response.json();
      setSeriesImages(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
    setSelectedStudy(null);
    setSeries([]);
    setSelectedSeries(null);
    setSeriesImages(null);
    setShowImageViewer(false);
    fetchStudies(patient.subject_id);
  };

  const handleStudySelect = (study: Study) => {
    setSelectedStudy(study);
    setSelectedSeries(null);
    setSeriesImages(null);
    setShowImageViewer(false);
    fetchSeries(study.study_uid);
  };

  const handleSeriesSelect = (series: Series) => {
    setSelectedSeries(series);
    setShowImageViewer(true);
    fetchSeriesImages(series.series_uid);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading DICOM data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">DICOM Viewer</h1>
            <div className="flex items-center space-x-6">
              {stats && (
                <div className="flex space-x-6 text-sm text-gray-600">
                  <span>Patients: {stats.total_patients}</span>
                  <span>Studies: {stats.total_studies}</span>
                  <span>Series: {stats.total_series}</span>
                </div>
              )}
              <Link
                href="/analysis"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Data Analysis
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Patients Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Patients ({patients.length})</h2>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {patients.map((patient) => (
                <div
                  key={patient.subject_id}
                  className={`p-3 rounded cursor-pointer transition-colors ${
                    selectedPatient?.subject_id === patient.subject_id
                      ? 'bg-blue-100 border-blue-300'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  onClick={() => handlePatientSelect(patient)}
                >
                  <div className="font-medium">{patient.subject_id}</div>
                  <div className="text-sm text-gray-600">
                    {patient.study_count} studies, {patient.series_count} series
                  </div>
                  <div className="text-xs text-gray-500">
                    {patient.modalities.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Studies Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              Studies {selectedPatient && `(${selectedPatient.subject_id})`}
            </h2>
            {selectedPatient ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {studies.map((study) => (
                  <div
                    key={study.study_uid}
                    className={`p-3 rounded cursor-pointer transition-colors ${
                      selectedStudy?.study_uid === study.study_uid
                        ? 'bg-green-100 border-green-300'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                    onClick={() => handleStudySelect(study)}
                  >
                    <div className="font-medium">{study.study_date}</div>
                    <div className="text-sm text-gray-600">{study.study_description}</div>
                    <div className="text-xs text-gray-500">
                      {study.series_count} series - {study.modalities.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8">
                Select a patient to view studies
              </div>
            )}
          </div>

          {/* Series Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              Series {selectedStudy && `(${selectedStudy.study_date})`}
            </h2>
            {selectedStudy ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {series.map((s) => (
                  <div
                    key={s.series_uid}
                    className={`p-3 rounded cursor-pointer transition-colors ${
                      selectedSeries?.series_uid === s.series_uid
                        ? 'bg-purple-100 border-purple-300'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                    onClick={() => handleSeriesSelect(s)}
                  >
                    <div className="font-medium">{s.modality}</div>
                    <div className="text-sm text-gray-600">{s.series_description}</div>
                    <div className="text-xs text-gray-500">
                      {s.number_of_images} images - {s.file_size}
                    </div>
                    <div className="text-xs text-gray-400">{s.manufacturer}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8">
                Select a study to view series
              </div>
            )}
          </div>
        </div>

        {/* Statistics Panel */}
        {stats && (
          <div className="mt-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Dataset Statistics</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Modalities</h3>
                <div className="space-y-1">
                  {Object.entries(stats.modalities).map(([modality, count]) => (
                    <div key={modality} className="flex justify-between text-sm">
                      <span>{modality}</span>
                      <span className="text-gray-600">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Manufacturers</h3>
                <div className="space-y-1">
                  {Object.entries(stats.manufacturers).slice(0, 5).map(([manufacturer, count]) => (
                    <div key={manufacturer} className="flex justify-between text-sm">
                      <span className="truncate">{manufacturer}</span>
                      <span className="text-gray-600">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Date Range</h3>
                <div className="text-sm">
                  <div>From: {stats.date_range.earliest}</div>
                  <div>To: {stats.date_range.latest}</div>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Summary</h3>
                <div className="text-sm space-y-1">
                  <div>{stats.total_patients} Patients</div>
                  <div>{stats.total_studies} Studies</div>
                  <div>{stats.total_series} Series</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Image Viewer Modal */}
        {showImageViewer && selectedSeries && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
              <div className="flex justify-between items-center p-4 border-b">
                <h3 className="text-lg font-semibold">
                  {selectedSeries.modality} - {selectedSeries.series_description}
                </h3>
                <button
                  onClick={() => setShowImageViewer(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="p-4 max-h-[80vh] overflow-y-auto">
                {seriesImages ? (
                  <div>
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-700 mb-2">Series Information</h4>
                      <div className="bg-gray-50 p-3 rounded">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Modality:</span> {seriesImages.series_info.modality}
                          </div>
                          <div>
                            <span className="font-medium">Total Images:</span> {seriesImages.total_images}
                          </div>
                          <div>
                            <span className="font-medium">Description:</span> {seriesImages.series_info.description}
                          </div>
                          <div>
                            <span className="font-medium">Manufacturer:</span> {seriesImages.series_info.manufacturer}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Images ({seriesImages.images.length})</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {seriesImages.images.map((image) => (
                          <div key={image.image_index} className="bg-gray-50 p-3 rounded">
                            <div className="font-medium text-sm">Image {image.image_index + 1}</div>
                            <div className="text-xs text-gray-600 mt-1">
                              <div>File: {image.file_name}</div>
                              <div>Size: {(image.file_size / 1024).toFixed(1)} KB</div>
                            </div>
                            <div className="mt-2 bg-gray-200 h-32 rounded flex items-center justify-center text-gray-500 text-sm">
                              DICOM Image Preview
                              <br />
                              (Requires DICOM viewer)
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-lg">Loading images...</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
