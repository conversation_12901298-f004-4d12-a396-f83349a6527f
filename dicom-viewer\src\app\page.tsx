'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Patient {
  subject_id: string;
  study_count: number;
  series_count: number;
  modalities: string[];
  study_dates: string[];
}

interface Study {
  study_uid: string;
  study_date: string;
  study_description: string;
  series_count: number;
  modalities: string[];
}

interface Series {
  series_uid: string;
  series_description: string;
  modality: string;
  manufacturer: string;
  sop_class_name: string;
  number_of_images: number;
  file_size: string;
  file_location: string;
}

interface Image {
  image_index: number;
  file_name: string;
  file_path: string;
  file_size: number;
  is_dicom?: boolean;
  thumbnail_url?: string;
  image_url?: string;
  instance_number?: number;
  rows?: number;
  columns?: number;
  pixel_spacing?: number[];
  slice_thickness?: number;
  window_center?: number;
  window_width?: number;
  has_pixel_data?: boolean;
}

interface SeriesImages {
  images: Image[];
  series_uid: string;
  total_images: number;
  series_info: {
    modality: string;
    description: string;
    manufacturer: string;
  };
}

interface Stats {
  total_patients: number;
  total_studies: number;
  total_series: number;
  modalities: Record<string, number>;
  manufacturers: Record<string, number>;
  date_range: {
    earliest: string;
    latest: string;
  };
}

export default function Home() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [studies, setStudies] = useState<Study[]>([]);
  const [selectedStudy, setSelectedStudy] = useState<Study | null>(null);
  const [series, setSeries] = useState<Series[]>([]);
  const [selectedSeries, setSelectedSeries] = useState<Series | null>(null);
  const [seriesImages, setSeriesImages] = useState<SeriesImages | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [windowCenter, setWindowCenter] = useState<number | null>(null);
  const [windowWidth, setWindowWidth] = useState<number | null>(null);
  const [imageScale, setImageScale] = useState(1);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showImageViewer, setShowImageViewer] = useState(false);

  const API_BASE = 'http://localhost:8000';

  useEffect(() => {
    fetchPatients();
    fetchStats();
  }, []);

  const fetchPatients = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/patients`);
      if (!response.ok) throw new Error('Failed to fetch patients');
      const data = await response.json();
      setPatients(data.patients);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/metadata/stats`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Failed to fetch stats:', err);
    }
  };

  const fetchStudies = async (subjectId: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/patients/${subjectId}/studies`);
      if (!response.ok) throw new Error('Failed to fetch studies');
      const data = await response.json();
      setStudies(data.studies);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchSeries = async (studyUid: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/studies/${studyUid}/series`);
      if (!response.ok) throw new Error('Failed to fetch series');
      const data = await response.json();
      setSeries(data.series);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchSeriesImages = async (seriesUid: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/series/${seriesUid}/images`);
      if (!response.ok) throw new Error('Failed to fetch series images');
      const data = await response.json();
      setSeriesImages(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
    setSelectedStudy(null);
    setSeries([]);
    setSelectedSeries(null);
    setSeriesImages(null);
    setShowImageViewer(false);
    fetchStudies(patient.subject_id);
  };

  const handleStudySelect = (study: Study) => {
    setSelectedStudy(study);
    setSelectedSeries(null);
    setSeriesImages(null);
    setShowImageViewer(false);
    fetchSeries(study.study_uid);
  };

  const handleSeriesSelect = (series: Series) => {
    setSelectedSeries(series);
    setShowImageViewer(true);
    fetchSeriesImages(series.series_uid);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">正在加载DICOM数据...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl text-red-600">错误: {error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">DICOM 影像查看器</h1>
            <div className="flex items-center space-x-6">
              {stats && (
                <div className="flex space-x-6 text-sm text-gray-600">
                  <span>患者: {stats.total_patients}</span>
                  <span>研究: {stats.total_studies}</span>
                  <span>序列: {stats.total_series}</span>
                </div>
              )}
              <Link
                href="/analysis"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                数据分析
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Patients Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">患者列表 ({patients.length})</h2>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {patients.map((patient) => (
                <div
                  key={patient.subject_id}
                  className={`p-3 rounded cursor-pointer transition-colors ${
                    selectedPatient?.subject_id === patient.subject_id
                      ? 'bg-blue-100 border-blue-300'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  onClick={() => handlePatientSelect(patient)}
                >
                  <div className="font-medium">{patient.subject_id}</div>
                  <div className="text-sm text-gray-600">
                    {patient.study_count} 个研究, {patient.series_count} 个序列
                  </div>
                  <div className="text-xs text-gray-500">
                    {patient.modalities.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Studies Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              研究列表 {selectedPatient && `(${selectedPatient.subject_id})`}
            </h2>
            {selectedPatient ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {studies.map((study) => (
                  <div
                    key={study.study_uid}
                    className={`p-3 rounded cursor-pointer transition-colors ${
                      selectedStudy?.study_uid === study.study_uid
                        ? 'bg-green-100 border-green-300'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                    onClick={() => handleStudySelect(study)}
                  >
                    <div className="font-medium">{study.study_date}</div>
                    <div className="text-sm text-gray-600">{study.study_description}</div>
                    <div className="text-xs text-gray-500">
                      {study.series_count} 个序列 - {study.modalities.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8">
                请选择患者以查看研究
              </div>
            )}
          </div>

          {/* Series Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              序列列表 {selectedStudy && `(${selectedStudy.study_date})`}
            </h2>
            {selectedStudy ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {series.map((s) => (
                  <div
                    key={s.series_uid}
                    className={`p-3 rounded cursor-pointer transition-colors ${
                      selectedSeries?.series_uid === s.series_uid
                        ? 'bg-purple-100 border-purple-300'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                    onClick={() => handleSeriesSelect(s)}
                  >
                    <div className="font-medium">{s.modality}</div>
                    <div className="text-sm text-gray-600">{s.series_description}</div>
                    <div className="text-xs text-gray-500">
                      {s.number_of_images} 张图像 - {s.file_size}
                    </div>
                    <div className="text-xs text-gray-400">{s.manufacturer}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8">
                请选择研究以查看序列
              </div>
            )}
          </div>
        </div>

        {/* Statistics Panel */}
        {stats && (
          <div className="mt-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">数据集统计</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">成像模态</h3>
                <div className="space-y-1">
                  {Object.entries(stats.modalities).map(([modality, count]) => (
                    <div key={modality} className="flex justify-between text-sm">
                      <span>{modality}</span>
                      <span className="text-gray-600">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">设备制造商</h3>
                <div className="space-y-1">
                  {Object.entries(stats.manufacturers).slice(0, 5).map(([manufacturer, count]) => (
                    <div key={manufacturer} className="flex justify-between text-sm">
                      <span className="truncate">{manufacturer}</span>
                      <span className="text-gray-600">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">日期范围</h3>
                <div className="text-sm">
                  <div>起始: {stats.date_range.earliest}</div>
                  <div>结束: {stats.date_range.latest}</div>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">数据概览</h3>
                <div className="text-sm space-y-1">
                  <div>{stats.total_patients} 名患者</div>
                  <div>{stats.total_studies} 个研究</div>
                  <div>{stats.total_series} 个序列</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Image Viewer Modal */}
        {showImageViewer && selectedSeries && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
              <div className="flex justify-between items-center p-4 border-b">
                <h3 className="text-lg font-semibold">
                  {selectedSeries.modality} - {selectedSeries.series_description}
                </h3>
                <button
                  onClick={() => setShowImageViewer(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="p-4 max-h-[80vh] overflow-y-auto">
                {seriesImages ? (
                  <div>
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-700 mb-2">序列信息</h4>
                      <div className="bg-gray-50 p-3 rounded">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">成像模态:</span> {seriesImages.series_info.modality}
                          </div>
                          <div>
                            <span className="font-medium">图像总数:</span> {seriesImages.total_images}
                          </div>
                          <div>
                            <span className="font-medium">序列描述:</span> {seriesImages.series_info.description}
                          </div>
                          <div>
                            <span className="font-medium">设备制造商:</span> {seriesImages.series_info.manufacturer}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">图像查看器</h4>
                      {seriesImages.images.length > 0 && (
                        <div className="space-y-4">
                          {/* 图像工具栏 */}
                          <div className="bg-gray-100 p-3 rounded flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => setImageScale(Math.max(0.1, imageScale - 0.1))}
                                  className="px-2 py-1 bg-gray-600 text-white rounded text-sm"
                                >
                                  缩小
                                </button>
                                <span className="text-sm font-medium">
                                  {Math.round(imageScale * 100)}%
                                </span>
                                <button
                                  onClick={() => setImageScale(Math.min(5, imageScale + 0.1))}
                                  className="px-2 py-1 bg-gray-600 text-white rounded text-sm"
                                >
                                  放大
                                </button>
                              </div>
                              <button
                                onClick={() => {
                                  setImageScale(1);
                                  setImagePosition({ x: 0, y: 0 });
                                }}
                                className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                              >
                                重置视图
                              </button>
                            </div>
                            <div className="text-sm text-gray-600">
                              拖拽图像可平移 | 滚轮可缩放
                            </div>
                          </div>

                          {/* 主图像显示区域 */}
                          <div className="bg-black rounded-lg p-4">
                            <DICOMImageViewer
                              image={seriesImages.images[currentImageIndex]}
                              apiBase={API_BASE}
                              windowCenter={windowCenter}
                              windowWidth={windowWidth}
                              scale={imageScale}
                              position={imagePosition}
                              onScaleChange={setImageScale}
                              onPositionChange={setImagePosition}
                            />
                          </div>

                          {/* 窗宽窗位控制 */}
                          {seriesImages.images[currentImageIndex].is_dicom && (
                            <div className="bg-gray-50 p-4 rounded">
                              <h5 className="font-medium text-gray-700 mb-3">窗宽窗位调节</h5>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    窗位 (Window Center)
                                  </label>
                                  <input
                                    type="range"
                                    min="-1000"
                                    max="1000"
                                    step="10"
                                    value={windowCenter || seriesImages.images[currentImageIndex].window_center || 40}
                                    onChange={(e) => setWindowCenter(Number(e.target.value))}
                                    className="w-full"
                                  />
                                  <div className="text-xs text-gray-600 mt-1">
                                    当前值: {windowCenter || seriesImages.images[currentImageIndex].window_center || 40}
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    窗宽 (Window Width)
                                  </label>
                                  <input
                                    type="range"
                                    min="1"
                                    max="2000"
                                    step="10"
                                    value={windowWidth || seriesImages.images[currentImageIndex].window_width || 400}
                                    onChange={(e) => setWindowWidth(Number(e.target.value))}
                                    className="w-full"
                                  />
                                  <div className="text-xs text-gray-600 mt-1">
                                    当前值: {windowWidth || seriesImages.images[currentImageIndex].window_width || 400}
                                  </div>
                                </div>
                              </div>
                              <div className="mt-3 flex space-x-2">
                                <button
                                  onClick={() => {
                                    setWindowCenter(40);
                                    setWindowWidth(400);
                                  }}
                                  className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                                >
                                  软组织
                                </button>
                                <button
                                  onClick={() => {
                                    setWindowCenter(300);
                                    setWindowWidth(1500);
                                  }}
                                  className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                                >
                                  骨窗
                                </button>
                                <button
                                  onClick={() => {
                                    setWindowCenter(-600);
                                    setWindowWidth(1600);
                                  }}
                                  className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                                >
                                  肺窗
                                </button>
                                <button
                                  onClick={() => {
                                    setWindowCenter(null);
                                    setWindowWidth(null);
                                  }}
                                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                                >
                                  重置
                                </button>
                              </div>
                            </div>
                          )}

                          {/* 图像导航控制 */}
                          <div className="flex items-center justify-between">
                            <button
                              onClick={() => setCurrentImageIndex(Math.max(0, currentImageIndex - 1))}
                              disabled={currentImageIndex === 0}
                              className="px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400"
                            >
                              上一张
                            </button>

                            <span className="text-sm text-gray-600">
                              {currentImageIndex + 1} / {seriesImages.images.length}
                            </span>

                            <button
                              onClick={() => setCurrentImageIndex(Math.min(seriesImages.images.length - 1, currentImageIndex + 1))}
                              disabled={currentImageIndex === seriesImages.images.length - 1}
                              className="px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400"
                            >
                              下一张
                            </button>
                          </div>

                          {/* 图像信息 */}
                          <div className="bg-gray-50 p-3 rounded">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>文件名: {seriesImages.images[currentImageIndex].file_name}</div>
                              <div>大小: {(seriesImages.images[currentImageIndex].file_size / 1024).toFixed(1)} KB</div>
                              {seriesImages.images[currentImageIndex].is_dicom && (
                                <>
                                  <div>实例号: {seriesImages.images[currentImageIndex].instance_number}</div>
                                  <div>图像尺寸: {seriesImages.images[currentImageIndex].rows} × {seriesImages.images[currentImageIndex].columns}</div>
                                  <div>像素间距: {seriesImages.images[currentImageIndex].pixel_spacing?.join(' × ')} mm</div>
                                  <div>层厚: {seriesImages.images[currentImageIndex].slice_thickness} mm</div>
                                </>
                              )}
                            </div>
                          </div>

                          {/* 缩略图导航 */}
                          <div>
                            <h5 className="font-medium text-gray-700 mb-2">缩略图导航</h5>
                            <div className="flex space-x-2 overflow-x-auto pb-2">
                              {seriesImages.images.map((image, index) => (
                                <div
                                  key={image.image_index}
                                  className={`flex-shrink-0 cursor-pointer border-2 rounded ${
                                    index === currentImageIndex ? 'border-blue-500' : 'border-gray-300'
                                  }`}
                                  onClick={() => setCurrentImageIndex(index)}
                                >
                                  <DICOMThumbnail
                                    image={image}
                                    apiBase={API_BASE}
                                    size={64}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-lg">正在加载图像...</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// DICOM图像查看器组件
function DICOMImageViewer({
  image,
  apiBase,
  windowCenter,
  windowWidth,
  scale = 1,
  position = { x: 0, y: 0 },
  onScaleChange,
  onPositionChange
}: {
  image: Image;
  apiBase: string;
  windowCenter?: number | null;
  windowWidth?: number | null;
  scale?: number;
  position?: { x: number; y: number };
  onScaleChange?: (scale: number) => void;
  onPositionChange?: (position: { x: number; y: number }) => void;
}) {
  const [imageData, setImageData] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (image.is_dicom && image.image_url) {
      setLoading(true);
      setError(null);

      // 构建带窗宽窗位参数的URL
      let url = `${apiBase}${image.image_url}`;
      const params = new URLSearchParams();

      if (windowCenter !== null && windowCenter !== undefined) {
        params.append('wc', windowCenter.toString());
      }
      if (windowWidth !== null && windowWidth !== undefined) {
        params.append('ww', windowWidth.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      fetch(url)
        .then(response => response.json())
        .then(data => {
          if (data.success && data.image_data) {
            setImageData(data.image_data);
          } else {
            setError(data.error || '无法加载图像');
          }
        })
        .catch(err => {
          setError('加载图像时出错');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
      setError('非DICOM文件或无图像数据');
    }
  }, [image, apiBase, windowCenter, windowWidth]);

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && onPositionChange) {
      onPositionChange({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (onScaleChange) {
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.max(0.1, Math.min(5, scale + delta));
      onScaleChange(newScale);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 text-white">
        <div>正在加载图像...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-white">
        <div>错误: {error}</div>
      </div>
    );
  }

  if (imageData) {
    return (
      <div
        className="flex items-center justify-center h-96 overflow-hidden cursor-move"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      >
        <img
          src={imageData}
          alt={`DICOM图像 ${image.file_name}`}
          className="max-w-none max-h-none object-contain select-none"
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transformOrigin: 'center center'
          }}
          draggable={false}
        />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center h-96 text-white">
      <div>无图像数据</div>
    </div>
  );
}

// DICOM缩略图组件
function DICOMThumbnail({ image, apiBase, size = 64 }: { image: Image; apiBase: string; size?: number }) {
  const [thumbnailData, setThumbnailData] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (image.is_dicom && image.thumbnail_url) {
      fetch(`${apiBase}${image.thumbnail_url}`)
        .then(response => response.json())
        .then(data => {
          if (data.success && data.thumbnail_data) {
            setThumbnailData(data.thumbnail_data);
          }
        })
        .catch(err => {
          console.error('加载缩略图时出错:', err);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [image, apiBase]);

  if (loading) {
    return (
      <div
        className="bg-gray-300 flex items-center justify-center text-xs text-gray-600"
        style={{ width: size, height: size }}
      >
        加载中...
      </div>
    );
  }

  if (thumbnailData) {
    return (
      <img
        src={thumbnailData}
        alt={`缩略图 ${image.file_name}`}
        style={{ width: size, height: size }}
        className="object-cover"
      />
    );
  }

  return (
    <div
      className="bg-gray-300 flex items-center justify-center text-xs text-gray-600"
      style={{ width: size, height: size }}
    >
      {image.image_index + 1}
    </div>
  );
}
