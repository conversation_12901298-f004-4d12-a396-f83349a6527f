# DICOM Viewer & Analysis System

一个现代化的DICOM数据管理、查看和分析系统，专为医学影像数据设计。

## 功能特性

### 🏥 数据管理
- **患者浏览**: 按患者ID组织和浏览DICOM数据
- **研究管理**: 查看每个患者的多个研究
- **序列查看**: 详细查看每个研究中的图像序列
- **元数据解析**: 自动解析CSV元数据文件

### 📊 数据分析
- **模态分布**: 统计不同成像模态（CT、MRI等）的分布
- **制造商分析**: 分析设备制造商分布
- **图像统计**: 每个患者的图像数量统计（最小值、最大值、平均值、中位数）
- **存储分析**: 数据集大小和存储统计
- **时间线分析**: 研究日期分布和趋势

### 🖼️ 图像查看
- **序列浏览**: 查看DICOM序列中的所有图像
- **图像信息**: 显示图像文件名、大小等详细信息
- **模态支持**: 支持多种DICOM模态

### 🎨 用户界面
- **响应式设计**: 适配桌面和移动设备
- **现代化UI**: 使用Tailwind CSS构建的清洁界面
- **交互式导航**: 直观的三级导航（患者→研究→序列）
- **实时统计**: 实时显示数据集统计信息

## 技术架构

### 前端
- **框架**: Next.js 15 + React 18
- **样式**: Tailwind CSS
- **语言**: TypeScript
- **特性**: 服务端渲染、热重载、响应式设计

### 后端
- **服务器**: Python HTTP服务器
- **数据处理**: 原生Python + CSV解析
- **API**: RESTful API设计
- **CORS**: 支持跨域请求

### 数据源
- **格式**: DICOM + CSV元数据
- **数据集**: NSCLC-Radiomics（非小细胞肺癌放射组学）
- **规模**: 1265个序列，涵盖多个患者和研究

## 项目结构

```
DICOM/
├── backend/                 # 后端API服务器
│   ├── basic_server.py     # 主服务器文件
│   ├── requirements.txt    # Python依赖
│   └── test_path.py       # 路径测试工具
├── dicom-viewer/           # 前端Next.js应用
│   ├── src/
│   │   └── app/
│   │       ├── page.tsx           # 主页面
│   │       └── analysis/
│   │           └── page.tsx       # 分析页面
│   ├── package.json
│   └── tailwind.config.ts
├── sourcedata/             # DICOM数据目录
│   └── a/manifest-1603198545583/
│       ├── metadata.csv    # 元数据文件
│       └── NSCLC-Radiomics/# DICOM文件
└── README.md
```

## API端点

### 数据查询
- `GET /api/patients` - 获取所有患者列表
- `GET /api/patients/{subject_id}/studies` - 获取患者的研究列表
- `GET /api/studies/{study_uid}/series` - 获取研究的序列列表
- `GET /api/series/{series_uid}/images` - 获取序列的图像列表

### 统计分析
- `GET /api/metadata/stats` - 获取基础统计信息
- `GET /api/analysis/overview` - 获取详细分析数据

## 快速开始

### 1. 启动后端服务器
```bash
cd backend
python basic_server.py
```
服务器将在 http://localhost:8000 启动

### 2. 启动前端应用
```bash
cd dicom-viewer
npm install
npm run dev -- -p 8080
```
应用将在 http://localhost:8080 启动

### 3. 访问应用
- 主页面: http://localhost:8080
- 分析页面: http://localhost:8080/analysis

## 使用说明

### 浏览数据
1. 在主页面左侧选择一个患者
2. 在中间面板选择一个研究
3. 在右侧面板选择一个序列
4. 点击序列可查看详细的图像信息

### 查看分析
1. 点击页面右上角的"Data Analysis"按钮
2. 查看各种统计图表和分析结果
3. 了解数据集的分布和特征

## 数据集信息

本系统使用NSCLC-Radiomics数据集，包含：
- **患者数量**: 422名患者
- **研究数量**: 422个研究
- **序列数量**: 1265个序列
- **主要模态**: CT、RTSTRUCT、SEG
- **时间跨度**: 2004-2014年
- **数据大小**: 约15GB

## 扩展功能

### 计划中的功能
- [ ] DICOM图像实际渲染和显示
- [ ] 图像测量和标注工具
- [ ] 3D重建和MPR视图
- [ ] 数据导出功能
- [ ] 用户认证和权限管理
- [ ] 数据库集成
- [ ] 高级分析算法

### 技术改进
- [ ] 添加pydicom支持真实DICOM解析
- [ ] 集成专业医学图像查看器
- [ ] 添加图像处理算法
- [ ] 性能优化和缓存
- [ ] 单元测试和集成测试

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目遵循MIT许可证。数据集遵循Creative Commons Attribution-NonCommercial 3.0 Unported License。
