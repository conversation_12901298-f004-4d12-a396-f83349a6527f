"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nfunction Home() {\n    var _seriesImages_images_currentImageIndex_pixel_spacing;\n    _s();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studies, setStudies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedStudy, setSelectedStudy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [series, setSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSeries, setSelectedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [seriesImages, setSeriesImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [windowCenter, setWindowCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageScale, setImageScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [imagePosition, setImagePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageViewer, setShowImageViewer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE = 'http://localhost:8000';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchPatients();\n            fetchStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const fetchPatients = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients\"));\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const data = await response.json();\n            setPatients(data.patients);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/metadata/stats\"));\n            if (!response.ok) throw new Error('Failed to fetch stats');\n            const data = await response.json();\n            setStats(data);\n        } catch (err) {\n            console.error('Failed to fetch stats:', err);\n        }\n    };\n    const fetchStudies = async (subjectId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients/\").concat(subjectId, \"/studies\"));\n            if (!response.ok) throw new Error('Failed to fetch studies');\n            const data = await response.json();\n            setStudies(data.studies);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeries = async (studyUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/studies/\").concat(studyUid, \"/series\"));\n            if (!response.ok) throw new Error('Failed to fetch series');\n            const data = await response.json();\n            setSeries(data.series);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeriesImages = async (seriesUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/series/\").concat(seriesUid, \"/images\"));\n            if (!response.ok) throw new Error('Failed to fetch series images');\n            const data = await response.json();\n            setSeriesImages(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const handlePatientSelect = (patient)=>{\n        setSelectedPatient(patient);\n        setSelectedStudy(null);\n        setSeries([]);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchStudies(patient.subject_id);\n    };\n    const handleStudySelect = (study)=>{\n        setSelectedStudy(study);\n        setSelectedSeries(null);\n        setSeriesImages(null);\n        setShowImageViewer(false);\n        fetchSeries(study.study_uid);\n    };\n    const handleSeriesSelect = (series)=>{\n        setSelectedSeries(series);\n        setShowImageViewer(true);\n        fetchSeriesImages(series.series_uid);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"正在加载DICOM数据...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl text-red-600\",\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"DICOM 影像查看器\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"患者: \",\n                                                    stats.total_patients\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"研究: \",\n                                                    stats.total_studies\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"序列: \",\n                                                    stats.total_series\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/analysis\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                        children: \"数据分析\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"患者列表 (\",\n                                            patients.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.subject_id) === patient.subject_id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handlePatientSelect(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: patient.subject_id\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            patient.study_count,\n                                                            \" 个研究, \",\n                                                            patient.series_count,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: patient.modalities.join(', ')\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, patient.subject_id, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"研究列表 \",\n                                            selectedPatient && \"(\".concat(selectedPatient.subject_id, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedPatient ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: studies.map((study)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedStudy === null || selectedStudy === void 0 ? void 0 : selectedStudy.study_uid) === study.study_uid ? 'bg-green-100 border-green-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleStudySelect(study),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: study.study_date\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: study.study_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            study.series_count,\n                                                            \" 个序列 - \",\n                                                            study.modalities.join(', ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, study.study_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择患者以查看研究\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"序列列表 \",\n                                            selectedStudy && \"(\".concat(selectedStudy.study_date, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedStudy ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: series.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedSeries === null || selectedSeries === void 0 ? void 0 : selectedSeries.series_uid) === s.series_uid ? 'bg-purple-100 border-purple-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleSeriesSelect(s),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: s.modality\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: s.series_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            s.number_of_images,\n                                                            \" 张图像 - \",\n                                                            s.file_size\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: s.manufacturer\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, s.series_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"请选择研究以查看序列\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"数据集统计\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"成像模态\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.modalities).map((param)=>{\n                                                    let [modality, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: modality\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, modality, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"设备制造商\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.manufacturers).slice(0, 5).map((param)=>{\n                                                    let [manufacturer, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: manufacturer\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, manufacturer, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"起始: \",\n                                                            stats.date_range.earliest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"结束: \",\n                                                            stats.date_range.latest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_patients,\n                                                            \" 名患者\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_studies,\n                                                            \" 个研究\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_series,\n                                                            \" 个序列\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this),\n                    showImageViewer && selectedSeries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center p-4 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                selectedSeries.modality,\n                                                \" - \",\n                                                selectedSeries.series_description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageViewer(false),\n                                            className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 max-h-[80vh] overflow-y-auto\",\n                                    children: seriesImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"序列信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-3 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"成像模态:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.modality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"图像总数:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.total_images\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"序列描述:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.description\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"设备制造商:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        seriesImages.series_info.manufacturer\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                        children: \"图像查看器\"\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    seriesImages.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-100 p-3 rounded flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setImageScale(Math.max(0.1, imageScale - 0.1)),\n                                                                                        className: \"px-2 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                        children: \"缩小\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 409,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: [\n                                                                                            Math.round(imageScale * 100),\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 415,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setImageScale(Math.min(5, imageScale + 0.1)),\n                                                                                        className: \"px-2 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                        children: \"放大\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 418,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setImageScale(1);\n                                                                                    setImagePosition({\n                                                                                        x: 0,\n                                                                                        y: 0\n                                                                                    });\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                                                                                children: \"重置视图\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"拖拽图像可平移 | 滚轮可缩放\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMImageViewer, {\n                                                                    image: seriesImages.images[currentImageIndex],\n                                                                    apiBase: API_BASE,\n                                                                    windowCenter: windowCenter,\n                                                                    windowWidth: windowWidth,\n                                                                    scale: imageScale,\n                                                                    position: imagePosition,\n                                                                    onScaleChange: setImageScale,\n                                                                    onPositionChange: setImagePosition\n                                                                }, void 0, false, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            seriesImages.images[currentImageIndex].is_dicom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-4 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-700 mb-3\",\n                                                                        children: \"窗宽窗位调节\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                        children: \"窗位 (Window Center)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 460,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"range\",\n                                                                                        min: \"-1000\",\n                                                                                        max: \"1000\",\n                                                                                        step: \"10\",\n                                                                                        value: windowCenter || seriesImages.images[currentImageIndex].window_center || 40,\n                                                                                        onChange: (e)=>setWindowCenter(Number(e.target.value)),\n                                                                                        className: \"w-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"当前值: \",\n                                                                                            windowCenter || seriesImages.images[currentImageIndex].window_center || 40\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 472,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                        children: \"窗宽 (Window Width)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 477,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"range\",\n                                                                                        min: \"1\",\n                                                                                        max: \"2000\",\n                                                                                        step: \"10\",\n                                                                                        value: windowWidth || seriesImages.images[currentImageIndex].window_width || 400,\n                                                                                        onChange: (e)=>setWindowWidth(Number(e.target.value)),\n                                                                                        className: \"w-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 480,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"当前值: \",\n                                                                                            windowWidth || seriesImages.images[currentImageIndex].window_width || 400\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 489,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3 flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(40);\n                                                                                    setWindowWidth(400);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"软组织\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(300);\n                                                                                    setWindowWidth(1500);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"骨窗\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(-600);\n                                                                                    setWindowWidth(1600);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-gray-600 text-white rounded text-sm\",\n                                                                                children: \"肺窗\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setWindowCenter(null);\n                                                                                    setWindowWidth(null);\n                                                                                },\n                                                                                className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                                                                                children: \"重置\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.max(0, currentImageIndex - 1)),\n                                                                        disabled: currentImageIndex === 0,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"上一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            currentImageIndex + 1,\n                                                                            \" / \",\n                                                                            seriesImages.images.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setCurrentImageIndex(Math.min(seriesImages.images.length - 1, currentImageIndex + 1)),\n                                                                        disabled: currentImageIndex === seriesImages.images.length - 1,\n                                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded disabled:bg-gray-400\",\n                                                                        children: \"下一张\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 p-3 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"文件名: \",\n                                                                                seriesImages.images[currentImageIndex].file_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"大小: \",\n                                                                                (seriesImages.images[currentImageIndex].file_size / 1024).toFixed(1),\n                                                                                \" KB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        seriesImages.images[currentImageIndex].is_dicom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"实例号: \",\n                                                                                        seriesImages.images[currentImageIndex].instance_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"图像尺寸: \",\n                                                                                        seriesImages.images[currentImageIndex].rows,\n                                                                                        \" \\xd7 \",\n                                                                                        seriesImages.images[currentImageIndex].columns\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"像素间距: \",\n                                                                                        (_seriesImages_images_currentImageIndex_pixel_spacing = seriesImages.images[currentImageIndex].pixel_spacing) === null || _seriesImages_images_currentImageIndex_pixel_spacing === void 0 ? void 0 : _seriesImages_images_currentImageIndex_pixel_spacing.join(' × '),\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"层厚: \",\n                                                                                        seriesImages.images[currentImageIndex].slice_thickness,\n                                                                                        \" mm\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-700 mb-2\",\n                                                                        children: \"缩略图导航\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                                                        children: seriesImages.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 cursor-pointer border-2 rounded \".concat(index === currentImageIndex ? 'border-blue-500' : 'border-gray-300'),\n                                                                                onClick: ()=>setCurrentImageIndex(index),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DICOMThumbnail, {\n                                                                                    image: image,\n                                                                                    apiBase: API_BASE,\n                                                                                    size: 64\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, image.image_index, false, {\n                                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg\",\n                                            children: \"正在加载图像...\"\n                                        }, void 0, false, {\n                                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"JH/+lAfNfmYf6VxJ7qSdxybadk0=\");\n_c = Home;\n// DICOM图像查看器组件\nfunction DICOMImageViewer(param) {\n    let { image, apiBase, windowCenter, windowWidth, scale = 1, position = {\n        x: 0,\n        y: 0\n    }, onScaleChange, onPositionChange } = param;\n    _s1();\n    const [imageData, setImageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMImageViewer.useEffect\": ()=>{\n            if (image.is_dicom && image.image_url) {\n                setLoading(true);\n                setError(null);\n                // 构建带窗宽窗位参数的URL\n                let url = \"\".concat(apiBase).concat(image.image_url);\n                const params = new URLSearchParams();\n                if (windowCenter !== null && windowCenter !== undefined) {\n                    params.append('wc', windowCenter.toString());\n                }\n                if (windowWidth !== null && windowWidth !== undefined) {\n                    params.append('ww', windowWidth.toString());\n                }\n                if (params.toString()) {\n                    url += \"?\".concat(params.toString());\n                }\n                fetch(url).then({\n                    \"DICOMImageViewer.useEffect\": (response)=>response.json()\n                }[\"DICOMImageViewer.useEffect\"]).then({\n                    \"DICOMImageViewer.useEffect\": (data)=>{\n                        if (data.success && data.image_data) {\n                            setImageData(data.image_data);\n                        } else {\n                            setError(data.error || '无法加载图像');\n                        }\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).catch({\n                    \"DICOMImageViewer.useEffect\": (err)=>{\n                        setError('加载图像时出错');\n                    }\n                }[\"DICOMImageViewer.useEffect\"]).finally({\n                    \"DICOMImageViewer.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMImageViewer.useEffect\"]);\n            } else {\n                setLoading(false);\n                setError('非DICOM文件或无图像数据');\n            }\n        }\n    }[\"DICOMImageViewer.useEffect\"], [\n        image,\n        apiBase,\n        windowCenter,\n        windowWidth\n    ]);\n    // 鼠标事件处理\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX - position.x,\n            y: e.clientY - position.y\n        });\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging && onPositionChange) {\n            onPositionChange({\n                x: e.clientX - dragStart.x,\n                y: e.clientY - dragStart.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    const handleWheel = (e)=>{\n        e.preventDefault();\n        if (onScaleChange) {\n            const delta = e.deltaY > 0 ? -0.1 : 0.1;\n            const newScale = Math.max(0.1, Math.min(5, scale + delta));\n            onScaleChange(newScale);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"正在加载图像...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 711,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 710,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"错误: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 719,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 718,\n            columnNumber: 7\n        }, this);\n    }\n    if (imageData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: imageData,\n                alt: \"DICOM图像 \".concat(image.file_name),\n                className: \"max-w-full max-h-96 object-contain\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 727,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 726,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-96 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"无图像数据\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 738,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 737,\n        columnNumber: 5\n    }, this);\n}\n_s1(DICOMImageViewer, \"JKsQllcoW51Mx6pMWlR7RgdTwMo=\");\n_c1 = DICOMImageViewer;\n// DICOM缩略图组件\nfunction DICOMThumbnail(param) {\n    let { image, apiBase, size = 64 } = param;\n    _s2();\n    const [thumbnailData, setThumbnailData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DICOMThumbnail.useEffect\": ()=>{\n            if (image.is_dicom && image.thumbnail_url) {\n                fetch(\"\".concat(apiBase).concat(image.thumbnail_url)).then({\n                    \"DICOMThumbnail.useEffect\": (response)=>response.json()\n                }[\"DICOMThumbnail.useEffect\"]).then({\n                    \"DICOMThumbnail.useEffect\": (data)=>{\n                        if (data.success && data.thumbnail_data) {\n                            setThumbnailData(data.thumbnail_data);\n                        }\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).catch({\n                    \"DICOMThumbnail.useEffect\": (err)=>{\n                        console.error('加载缩略图时出错:', err);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]).finally({\n                    \"DICOMThumbnail.useEffect\": ()=>{\n                        setLoading(false);\n                    }\n                }[\"DICOMThumbnail.useEffect\"]);\n            } else {\n                setLoading(false);\n            }\n        }\n    }[\"DICOMThumbnail.useEffect\"], [\n        image,\n        apiBase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n            style: {\n                width: size,\n                height: size\n            },\n            children: \"加载中...\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 770,\n            columnNumber: 7\n        }, this);\n    }\n    if (thumbnailData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: thumbnailData,\n            alt: \"缩略图 \".concat(image.file_name),\n            style: {\n                width: size,\n                height: size\n            },\n            className: \"object-cover\"\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 781,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-300 flex items-center justify-center text-xs text-gray-600\",\n        style: {\n            width: size,\n            height: size\n        },\n        children: image.image_index + 1\n    }, void 0, false, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 791,\n        columnNumber: 5\n    }, this);\n}\n_s2(DICOMThumbnail, \"OFgfWwmGhuZAeKYBmxFIBQq2c+4=\");\n_c2 = DICOMThumbnail;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c1, \"DICOMImageViewer\");\n$RefreshReg$(_c2, \"DICOMThumbnail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});