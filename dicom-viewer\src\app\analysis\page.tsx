'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface AnalysisData {
  modality_distribution: Record<string, number>;
  manufacturer_distribution: Record<string, number>;
  images_per_patient: {
    min: number;
    max: number;
    avg: number;
    median: number;
  };
  study_dates_distribution: Record<string, number>;
  file_size_stats: {
    total_size_gb: number;
    avg_size_mb: number;
    largest_series_mb: number;
  };
}

export default function AnalysisPage() {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = 'http://localhost:8000';

  useEffect(() => {
    fetchAnalysisData();
  }, []);

  const fetchAnalysisData = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/analysis/overview`);
      if (!response.ok) throw new Error('Failed to fetch analysis data');
      const data = await response.json();
      setAnalysisData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading analysis data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-blue-600 hover:text-blue-800">
                ← 返回查看器
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">DICOM 数据分析</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {analysisData ? (
          <div className="space-y-8">
            {/* Modality Distribution */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Modality Distribution</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(analysisData.modality_distribution).map(([modality, count]) => (
                  <div key={modality} className="bg-gray-50 p-4 rounded">
                    <div className="text-lg font-medium">{modality}</div>
                    <div className="text-2xl font-bold text-blue-600">{count}</div>
                    <div className="text-sm text-gray-600">series</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Manufacturer Distribution */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Manufacturer Distribution</h2>
              <div className="space-y-2">
                {Object.entries(analysisData.manufacturer_distribution)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 10)
                  .map(([manufacturer, count]) => (
                    <div key={manufacturer} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">{manufacturer}</span>
                      <span className="text-blue-600 font-bold">{count} series</span>
                    </div>
                  ))}
              </div>
            </div>

            {/* Images per Patient Statistics */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Images per Patient Statistics</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-green-600">{analysisData.images_per_patient.min}</div>
                  <div className="text-sm text-gray-600">Minimum</div>
                </div>
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-blue-600">{analysisData.images_per_patient.avg.toFixed(1)}</div>
                  <div className="text-sm text-gray-600">Average</div>
                </div>
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-purple-600">{analysisData.images_per_patient.median}</div>
                  <div className="text-sm text-gray-600">Median</div>
                </div>
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-red-600">{analysisData.images_per_patient.max}</div>
                  <div className="text-sm text-gray-600">Maximum</div>
                </div>
              </div>
            </div>

            {/* File Size Statistics */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Storage Statistics</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-blue-600">{analysisData.file_size_stats.total_size_gb.toFixed(2)} GB</div>
                  <div className="text-sm text-gray-600">Total Dataset Size</div>
                </div>
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-green-600">{analysisData.file_size_stats.avg_size_mb.toFixed(1)} MB</div>
                  <div className="text-sm text-gray-600">Average Series Size</div>
                </div>
                <div className="bg-gray-50 p-4 rounded text-center">
                  <div className="text-2xl font-bold text-purple-600">{analysisData.file_size_stats.largest_series_mb.toFixed(1)} MB</div>
                  <div className="text-sm text-gray-600">Largest Series</div>
                </div>
              </div>
            </div>

            {/* Study Dates Timeline */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Study Timeline (by Year)</h2>
              <div className="space-y-2">
                {Object.entries(analysisData.study_dates_distribution)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([year, count]) => (
                    <div key={year} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">{year}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: `${(count / Math.max(...Object.values(analysisData.study_dates_distribution))) * 100}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-blue-600 font-bold w-12 text-right">{count}</span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-lg">No analysis data available</div>
          </div>
        )}
      </div>
    </div>
  );
}
