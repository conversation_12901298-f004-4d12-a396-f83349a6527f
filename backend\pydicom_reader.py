#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import io
from pathlib import Path
from typing import Dict, Optional

# 导入pydicom和相关库
try:
    import pydicom
    import numpy as np
    from PIL import Image
    PYDICOM_AVAILABLE = True
    print("✅ PyDicom库加载成功")
except ImportError as e:
    PYDICOM_AVAILABLE = False
    print(f"❌ PyDicom库不可用: {e}")

class PyDicomReader:
    """专门使用PyDicom的DICOM读取器"""
    
    def __init__(self, path: str):
        self.path = Path(path)
        self._dataset = None
        self._pixel_array = None
        
    def read_dicom(self) -> Dict:
        """使用PyDicom读取DICOM文件"""
        if not PYDICOM_AVAILABLE:
            raise ImportError("PyDicom库不可用")
            
        try:
            print(f"📖 使用PyDicom读取: {self.path.name}")
            
            # 使用pydicom读取DICOM文件
            self._dataset = pydicom.dcmread(str(self.path))
            print(f"✅ PyDicom成功读取DICOM文件")
            
            # 检查是否有像素数据
            if not hasattr(self._dataset, 'pixel_array'):
                raise ValueError("DICOM文件没有像素数据")
            
            # 获取像素数组
            self._pixel_array = self._dataset.pixel_array
            print(f"✅ 获取像素数组: {self._pixel_array.shape}, 数据类型: {self._pixel_array.dtype}")
            
            # 处理多维数组
            if len(self._pixel_array.shape) == 3:
                # 如果是3D，取中间切片
                slice_idx = self._pixel_array.shape[0] // 2
                self._pixel_array = self._pixel_array[slice_idx]
                print(f"✅ 选择切片 {slice_idx}: {self._pixel_array.shape}")
            
            # 获取像素统计
            pixel_min = float(np.min(self._pixel_array))
            pixel_max = float(np.max(self._pixel_array))
            pixel_mean = float(np.mean(self._pixel_array))
            
            print(f"✅ 像素统计 - 最小值: {pixel_min}, 最大值: {pixel_max}, 平均值: {pixel_mean:.2f}")
            
            # 获取DICOM元数据
            metadata = self._extract_metadata()
            
            return {
                'success': True,
                'reader': 'PyDicom',
                'image_shape': self._pixel_array.shape,
                'pixel_range': [pixel_min, pixel_max],
                'pixel_mean': pixel_mean,
                'metadata': metadata
            }
            
        except Exception as e:
            print(f"❌ PyDicom读取失败: {e}")
            raise
    
    def _extract_metadata(self) -> Dict:
        """提取DICOM元数据"""
        metadata = {}
        
        if self._dataset:
            # 基本信息
            metadata['PatientID'] = getattr(self._dataset, 'PatientID', 'Unknown')
            metadata['StudyInstanceUID'] = getattr(self._dataset, 'StudyInstanceUID', 'Unknown')
            metadata['SeriesInstanceUID'] = getattr(self._dataset, 'SeriesInstanceUID', 'Unknown')
            metadata['SOPInstanceUID'] = getattr(self._dataset, 'SOPInstanceUID', 'Unknown')
            
            # 图像信息
            metadata['Rows'] = getattr(self._dataset, 'Rows', 0)
            metadata['Columns'] = getattr(self._dataset, 'Columns', 0)
            metadata['PixelSpacing'] = getattr(self._dataset, 'PixelSpacing', [1.0, 1.0])
            metadata['SliceThickness'] = getattr(self._dataset, 'SliceThickness', 1.0)
            
            # 窗宽窗位
            metadata['WindowCenter'] = getattr(self._dataset, 'WindowCenter', None)
            metadata['WindowWidth'] = getattr(self._dataset, 'WindowWidth', None)
            
            # 设备信息
            metadata['Manufacturer'] = getattr(self._dataset, 'Manufacturer', 'Unknown')
            metadata['ManufacturerModelName'] = getattr(self._dataset, 'ManufacturerModelName', 'Unknown')
            
            # 图像类型
            metadata['Modality'] = getattr(self._dataset, 'Modality', 'Unknown')
            metadata['PhotometricInterpretation'] = getattr(self._dataset, 'PhotometricInterpretation', 'Unknown')
            
        return metadata
    
    def get_pixel_array(self):
        """获取像素数组"""
        if self._pixel_array is None:
            self.read_dicom()
        return self._pixel_array
    
    def create_image_base64(self, window_center: float = None, window_width: float = None) -> str:
        """创建Base64编码的图像"""
        try:
            if self._pixel_array is None:
                self.read_dicom()
            
            image_2d = self._pixel_array.copy()
            print(f"🎨 PyDicom创建图像: 形状={image_2d.shape}")
            
            # 获取默认窗宽窗位
            if window_center is None:
                if self._dataset and hasattr(self._dataset, 'WindowCenter'):
                    try:
                        wc = self._dataset.WindowCenter
                        window_center = float(wc[0] if isinstance(wc, list) else wc)
                    except:
                        window_center = 40
                else:
                    window_center = 40
            
            if window_width is None:
                if self._dataset and hasattr(self._dataset, 'WindowWidth'):
                    try:
                        ww = self._dataset.WindowWidth
                        window_width = float(ww[0] if isinstance(ww, list) else ww)
                    except:
                        window_width = 400
                else:
                    window_width = 400
            
            print(f"🎨 应用窗宽窗位: WC={window_center}, WW={window_width}")
            
            # 应用窗宽窗位
            min_val = window_center - window_width / 2
            max_val = window_center + window_width / 2
            
            # 裁剪像素值到窗口范围
            image_2d = np.clip(image_2d, min_val, max_val)
            
            # 缩放到0-255范围
            if max_val > min_val:
                image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
            else:
                image_2d = np.zeros_like(image_2d, dtype=np.uint8)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_2d, mode='L')
            
            # 转换为Base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            print(f"✅ PyDicom图像创建成功: {len(image_base64)} 字符")
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            print(f"❌ PyDicom图像创建失败: {e}")
            raise

def read_dicom_with_pydicom(file_path: str, window_center: float = None, window_width: float = None) -> str:
    """使用PyDicom读取DICOM并返回Base64图像"""
    try:
        print(f"📖 启动PyDicom DICOM读取器")
        
        if not PYDICOM_AVAILABLE:
            raise ImportError("PyDicom库不可用，请先安装: pip install pydicom numpy Pillow")
        
        reader = PyDicomReader(file_path)
        info = reader.read_dicom()
        
        print(f"✅ PyDicom读取成功:")
        print(f"   - 读取器: {info['reader']}")
        print(f"   - 图像形状: {info['image_shape']}")
        print(f"   - 像素范围: {info['pixel_range']}")
        print(f"   - 像素平均值: {info['pixel_mean']:.2f}")
        
        # 创建图像
        image_base64 = reader.create_image_base64(window_center, window_width)
        
        return image_base64
        
    except Exception as e:
        print(f"❌ PyDicom处理失败: {e}")
        raise

if __name__ == "__main__":
    # 测试代码
    if PYDICOM_AVAILABLE:
        print("✅ PyDicom DICOM读取器准备就绪")
    else:
        print("⚠️ PyDicom库不可用")
        print("请运行: pip install pydicom numpy Pillow")
