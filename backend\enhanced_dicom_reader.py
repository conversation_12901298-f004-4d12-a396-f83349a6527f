#!/usr/bin/env python3
"""增强的DICOM读取器，专门处理像素数据"""

import struct
import base64
from pathlib import Path
from typing import Dict, Optional, List, Tuple

class EnhancedDICOMReader:
    """增强的DICOM读取器，专注于像素数据提取"""
    
    def __init__(self):
        self.dicom_prefix = b'DICM'
    
    def read_dicom_raw(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM文件的原始数据"""
        try:
            with open(file_path, 'rb') as f:
                # 读取整个文件
                data = f.read()
                
                # 查找DICM标识
                dicm_pos = data.find(self.dicom_prefix)
                if dicm_pos == -1:
                    return None
                
                # 查找像素数据标签 (7FE0,0010)
                pixel_tag = b'\xE0\x7F\x10\x00'  # 小端字节序
                pixel_pos = data.find(pixel_tag, dicm_pos)
                
                if pixel_pos == -1:
                    return None
                
                # 尝试提取像素数据
                return self._extract_pixel_data_raw(data, pixel_pos, file_path.name)
                
        except Exception as e:
            print(f"Error reading DICOM raw data: {e}")
            return None
    
    def _extract_pixel_data_raw(self, data: bytes, pixel_pos: int, filename: str) -> Dict:
        """从原始数据中提取像素数据"""
        try:
            # 跳过标签 (4字节)
            pos = pixel_pos + 4
            
            # 读取VR (2字节)
            if pos + 2 > len(data):
                return None
            vr = data[pos:pos+2]
            pos += 2
            
            # 根据VR确定长度字段
            if vr in [b'OB', b'OW', b'OF', b'SQ', b'UT', b'UN']:
                # 跳过保留字节 (2字节)
                pos += 2
                # 读取长度 (4字节)
                if pos + 4 > len(data):
                    return None
                length = struct.unpack('<I', data[pos:pos+4])[0]
                pos += 4
            else:
                # 读取长度 (2字节)
                if pos + 2 > len(data):
                    return None
                length = struct.unpack('<H', data[pos:pos+2])[0]
                pos += 2
            
            # 提取像素数据
            if pos + length > len(data):
                length = len(data) - pos
            
            pixel_data = data[pos:pos+length]
            
            # 尝试确定图像尺寸
            width, height = self._guess_image_dimensions(len(pixel_data))
            
            return {
                'pixel_data': pixel_data,
                'width': width,
                'height': height,
                'data_length': len(pixel_data),
                'filename': filename,
                'vr': vr.decode('ascii', errors='ignore')
            }
            
        except Exception as e:
            print(f"Error extracting pixel data: {e}")
            return None
    
    def _guess_image_dimensions(self, data_length: int) -> Tuple[int, int]:
        """根据数据长度猜测图像尺寸"""
        # 常见的医学图像尺寸
        common_sizes = [
            (512, 512),   # 最常见
            (256, 256),
            (1024, 1024),
            (128, 128),
            (64, 64)
        ]
        
        for width, height in common_sizes:
            # 检查16位像素
            if data_length >= width * height * 2:
                return width, height
            # 检查8位像素
            if data_length >= width * height:
                return width, height
        
        # 如果没有匹配，尝试计算
        import math
        pixels = data_length // 2  # 假设16位
        side = int(math.sqrt(pixels))
        return side, side
    
    def create_image_from_raw_pixels(self, pixel_info: Dict, 
                                   window_center: float = 40, 
                                   window_width: float = 400) -> str:
        """从原始像素数据创建图像"""
        try:
            pixel_data = pixel_info['pixel_data']
            width = pixel_info['width']
            height = pixel_info['height']
            
            # 解析像素值
            pixels = []
            
            # 尝试16位解析
            if len(pixel_data) >= width * height * 2:
                for i in range(0, width * height * 2, 2):
                    if i + 1 < len(pixel_data):
                        # 小端字节序16位
                        pixel_value = struct.unpack('<H', pixel_data[i:i+2])[0]
                        pixels.append(pixel_value)
                    else:
                        pixels.append(0)
            else:
                # 尝试8位解析
                for i in range(min(len(pixel_data), width * height)):
                    pixels.append(pixel_data[i])
            
            # 如果像素数量不足，填充
            while len(pixels) < width * height:
                pixels.append(0)
            
            # 应用窗宽窗位并创建图像
            return self._create_real_image_svg(pixels, width, height, window_center, window_width)
            
        except Exception as e:
            print(f"Error creating image from raw pixels: {e}")
            return self._create_fallback_image(pixel_info['filename'])
    
    def _create_real_image_svg(self, pixels: List[int], width: int, height: int,
                              window_center: float, window_width: float) -> str:
        """创建真实的像素图像SVG"""
        try:
            # 计算统计信息
            min_val = min(pixels) if pixels else 0
            max_val = max(pixels) if pixels else 255
            
            # 窗宽窗位变换
            wc = window_center
            ww = window_width
            min_window = wc - ww / 2
            max_window = wc + ww / 2
            
            # 处理像素值
            processed_pixels = []
            for pixel in pixels:
                if pixel <= min_window:
                    gray = 0
                elif pixel >= max_window:
                    gray = 255
                else:
                    gray = int(255 * (pixel - min_window) / ww)
                processed_pixels.append(max(0, min(255, gray)))
            
            # 创建SVG图像
            display_size = 512
            svg_content = f'''<svg width="{display_size}" height="{display_size}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000"/>
                <g>'''
            
            # 采样显示像素（避免SVG过大）
            step = max(1, max(width, height) // display_size)
            
            for y in range(0, height, step):
                for x in range(0, width, step):
                    if y * width + x < len(processed_pixels):
                        gray_val = processed_pixels[y * width + x]
                        color = f"#{gray_val:02x}{gray_val:02x}{gray_val:02x}"
                        
                        svg_x = x * display_size // width
                        svg_y = y * display_size // height
                        size = max(1, step * display_size // max(width, height))
                        
                        svg_content += f'<rect x="{svg_x}" y="{svg_y}" width="{size}" height="{size}" fill="{color}"/>'
            
            # 添加信息叠加
            svg_content += f'''
                </g>
                <!-- 信息叠加 -->
                <rect x="10" y="10" width="200" height="80" fill="#000" opacity="0.8" rx="5"/>
                <text x="20" y="30" font-family="monospace" font-size="12" fill="#0f0">真实DICOM像素</text>
                <text x="20" y="45" font-family="monospace" font-size="10" fill="#0a0">范围: {min_val}-{max_val}</text>
                <text x="20" y="60" font-family="monospace" font-size="10" fill="#0a0">WC:{wc:.0f} WW:{ww:.0f}</text>
                <text x="20" y="75" font-family="monospace" font-size="10" fill="#0a0">{width}×{height}</text>
            </svg>'''
            
            # 转换为base64
            svg_bytes = svg_content.encode('utf-8')
            svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
            
            return f"data:image/svg+xml;base64,{svg_base64}"
            
        except Exception as e:
            print(f"Error creating real image SVG: {e}")
            return self._create_fallback_image("error")
    
    def _create_fallback_image(self, filename: str) -> str:
        """创建后备图像"""
        svg_content = f'''<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#1a1a1a"/>
            <text x="50%" y="50%" text-anchor="middle" font-family="monospace" font-size="16" fill="#ccc">
                无法解析像素数据
            </text>
            <text x="50%" y="60%" text-anchor="middle" font-family="monospace" font-size="12" fill="#999">
                {filename}
            </text>
        </svg>'''
        
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
        
        return f"data:image/svg+xml;base64,{svg_base64}"

# 测试函数
def test_enhanced_reader():
    """测试增强读取器"""
    reader = EnhancedDICOMReader()
    
    # 测试文件
    test_file = Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-001.dcm")
    
    if test_file.exists():
        print(f"测试文件: {test_file.name}")
        
        # 读取原始数据
        pixel_info = reader.read_dicom_raw(test_file)
        if pixel_info:
            print(f"✅ 提取像素数据成功:")
            print(f"   数据长度: {pixel_info['data_length']} 字节")
            print(f"   图像尺寸: {pixel_info['width']}×{pixel_info['height']}")
            print(f"   VR类型: {pixel_info['vr']}")
            
            # 生成图像
            image_data = reader.create_image_from_raw_pixels(pixel_info)
            print(f"✅ 生成图像: {len(image_data)} 字符")
        else:
            print("❌ 无法提取像素数据")
    else:
        print(f"❌ 测试文件不存在: {test_file}")

if __name__ == "__main__":
    test_enhanced_reader()
