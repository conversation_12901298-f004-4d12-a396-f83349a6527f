import http.server
import socketserver
import json
import csv
from pathlib import Path
from urllib.parse import urlparse
from collections import defaultdict

# 数据路径配置
DICOM_DATA_PATH = Path("../sourcedata/a/manifest-1603198545583")
METADATA_FILE = DICOM_DATA_PATH / "metadata.csv"

class DICOMHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.metadata = []
        self.load_metadata()
        super().__init__(*args, **kwargs)

    def load_metadata(self):
        """加载元数据CSV文件"""
        try:
            if METADATA_FILE.exists():
                with open(METADATA_FILE, 'r', encoding='utf-8') as file:
                    reader = csv.DictReader(file)
                    self.metadata = list(reader)
                print(f"Loaded metadata with {len(self.metadata)} records")
            else:
                print(f"Metadata file not found: {METADATA_FILE}")
        except Exception as e:
            print(f"Error loading metadata: {e}")

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            if path == '/api/patients':
                response = self.get_patients()
            elif path.startswith('/api/patients/') and path.endswith('/studies'):
                subject_id = path.split('/')[-2]
                response = self.get_patient_studies(subject_id)
            elif path.startswith('/api/studies/') and path.endswith('/series'):
                study_uid = path.split('/')[-2]
                response = self.get_study_series(study_uid)
            elif path == '/api/metadata/stats':
                response = self.get_metadata_stats()
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode())

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def get_patients(self):
        """获取所有患者列表"""
        if not self.metadata:
            return {"patients": [], "total": 0}

        # 按患者ID分组
        patients_data = defaultdict(list)
        for row in self.metadata:
            patients_data[row['Subject ID']].append(row)

        patients = []
        for subject_id, patient_rows in patients_data.items():
            # 获取唯一的研究UID
            study_uids = set(row['Study UID'] for row in patient_rows)
            modalities = set(row['Modality'] for row in patient_rows)
            study_dates = set(row['Study Date'] for row in patient_rows)

            patients.append({
                'subject_id': subject_id,
                'study_count': len(study_uids),
                'series_count': len(patient_rows),
                'modalities': list(modalities),
                'study_dates': list(study_dates)
            })

        return {"patients": sorted(patients, key=lambda x: x['subject_id']), "total": len(patients)}

    def get_patient_studies(self, subject_id):
        """获取特定患者的研究列表"""
        if not self.metadata:
            return {"studies": [], "subject_id": subject_id}

        # 过滤患者数据
        patient_data = [row for row in self.metadata if row['Subject ID'] == subject_id]

        # 按研究UID分组
        studies_data = defaultdict(list)
        for row in patient_data:
            studies_data[row['Study UID']].append(row)

        studies = []
        for study_uid, study_rows in studies_data.items():
            modalities = set(row['Modality'] for row in study_rows)

            studies.append({
                'study_uid': study_uid,
                'study_date': study_rows[0]['Study Date'],
                'study_description': study_rows[0]['Study Description'],
                'series_count': len(study_rows),
                'modalities': list(modalities)
            })

        return {"studies": studies, "subject_id": subject_id}

    def get_study_series(self, study_uid):
        """获取特定研究的序列列表"""
        if not self.metadata:
            return {"series": [], "study_uid": study_uid}

        # 过滤研究数据
        study_data = [row for row in self.metadata if row['Study UID'] == study_uid]

        series = []
        for row in study_data:
            series.append({
                'series_uid': row['Series UID'],
                'series_description': row['Series Description'],
                'modality': row['Modality'],
                'manufacturer': row['Manufacturer'],
                'sop_class_name': row['SOP Class Name'],
                'number_of_images': int(row['Number of Images']) if row['Number of Images'].isdigit() else 0,
                'file_size': row['File Size'],
                'file_location': row['File Location']
            })

        return {"series": series, "study_uid": study_uid}

    def get_metadata_stats(self):
        """获取元数据统计信息"""
        if not self.metadata:
            return {"error": "Metadata not loaded"}

        # 统计信息
        subject_ids = set(row['Subject ID'] for row in self.metadata)
        study_uids = set(row['Study UID'] for row in self.metadata)

        # 统计模态
        modalities = defaultdict(int)
        for row in self.metadata:
            modalities[row['Modality']] += 1

        # 统计制造商
        manufacturers = defaultdict(int)
        for row in self.metadata:
            manufacturers[row['Manufacturer']] += 1

        # 日期范围
        dates = [row['Study Date'] for row in self.metadata if row['Study Date']]

        stats = {
            "total_patients": len(subject_ids),
            "total_studies": len(study_uids),
            "total_series": len(self.metadata),
            "modalities": dict(modalities),
            "manufacturers": dict(manufacturers),
            "date_range": {
                "earliest": min(dates) if dates else "N/A",
                "latest": max(dates) if dates else "N/A"
            }
        }
        return stats

if __name__ == "__main__":
    PORT = 8000

    # 创建处理器实例
    handler = DICOMHandler

    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"DICOM API Server running on http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")
