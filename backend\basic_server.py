import http.server
import socketserver
import json
import csv
from pathlib import Path
from urllib.parse import urlparse, parse_qs
from collections import defaultdict
from dicom_processor import DICOMProcessor

# 数据路径配置
DICOM_DATA_PATH = Path("../sourcedata/a/manifest-1603198545583")
METADATA_FILE = DICOM_DATA_PATH / "metadata.csv"

class DICOMHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.metadata = []
        self.dicom_processor = DICOMProcessor(DICOM_DATA_PATH)
        self.load_metadata()
        super().__init__(*args, **kwargs)

    def load_metadata(self):
        """加载元数据CSV文件"""
        try:
            if METADATA_FILE.exists():
                with open(METADATA_FILE, 'r', encoding='utf-8') as file:
                    reader = csv.DictReader(file)
                    self.metadata = list(reader)
                print(f"Loaded metadata with {len(self.metadata)} records")
            else:
                print(f"Metadata file not found: {METADATA_FILE}")
        except Exception as e:
            print(f"Error loading metadata: {e}")

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            if path == '/api/patients':
                response = self.get_patients()
            elif path.startswith('/api/patients/') and path.endswith('/studies'):
                subject_id = path.split('/')[-2]
                response = self.get_patient_studies(subject_id)
            elif path.startswith('/api/studies/') and path.endswith('/series'):
                study_uid = path.split('/')[-2]
                response = self.get_study_series(study_uid)
            elif path == '/api/metadata/stats':
                response = self.get_metadata_stats()
            elif path.startswith('/api/series/') and '/images' in path:
                series_uid = path.split('/')[-2]
                response = self.get_series_images(series_uid)
            elif path == '/api/analysis/overview':
                response = self.get_analysis_overview()
            elif path.startswith('/api/dicom/image/'):
                # 获取DICOM图像数据
                file_path = path.replace('/api/dicom/image/', '')
                response = self.get_dicom_image(file_path, parsed_path.query)
            elif path.startswith('/api/dicom/thumbnail/'):
                # 获取DICOM缩略图
                file_path = path.replace('/api/dicom/thumbnail/', '')
                response = self.get_dicom_thumbnail(file_path)
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response).encode())
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode())

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def get_patients(self):
        """获取所有患者列表"""
        if not self.metadata:
            return {"patients": [], "total": 0}

        # 按患者ID分组
        patients_data = defaultdict(list)
        for row in self.metadata:
            patients_data[row['Subject ID']].append(row)

        patients = []
        for subject_id, patient_rows in patients_data.items():
            # 获取唯一的研究UID
            study_uids = set(row['Study UID'] for row in patient_rows)
            modalities = set(row['Modality'] for row in patient_rows)
            study_dates = set(row['Study Date'] for row in patient_rows)

            patients.append({
                'subject_id': subject_id,
                'study_count': len(study_uids),
                'series_count': len(patient_rows),
                'modalities': list(modalities),
                'study_dates': list(study_dates)
            })

        return {"patients": sorted(patients, key=lambda x: x['subject_id']), "total": len(patients)}

    def get_patient_studies(self, subject_id):
        """获取特定患者的研究列表"""
        if not self.metadata:
            return {"studies": [], "subject_id": subject_id}

        # 过滤患者数据
        patient_data = [row for row in self.metadata if row['Subject ID'] == subject_id]

        # 按研究UID分组
        studies_data = defaultdict(list)
        for row in patient_data:
            studies_data[row['Study UID']].append(row)

        studies = []
        for study_uid, study_rows in studies_data.items():
            modalities = set(row['Modality'] for row in study_rows)

            studies.append({
                'study_uid': study_uid,
                'study_date': study_rows[0]['Study Date'],
                'study_description': study_rows[0]['Study Description'],
                'series_count': len(study_rows),
                'modalities': list(modalities)
            })

        return {"studies": studies, "subject_id": subject_id}

    def get_study_series(self, study_uid):
        """获取特定研究的序列列表"""
        if not self.metadata:
            return {"series": [], "study_uid": study_uid}

        # 过滤研究数据
        study_data = [row for row in self.metadata if row['Study UID'] == study_uid]

        series = []
        for row in study_data:
            series.append({
                'series_uid': row['Series UID'],
                'series_description': row['Series Description'],
                'modality': row['Modality'],
                'manufacturer': row['Manufacturer'],
                'sop_class_name': row['SOP Class Name'],
                'number_of_images': int(row['Number of Images']) if row['Number of Images'].isdigit() else 0,
                'file_size': row['File Size'],
                'file_location': row['File Location']
            })

        return {"series": series, "study_uid": study_uid}

    def get_metadata_stats(self):
        """获取元数据统计信息"""
        if not self.metadata:
            return {"error": "Metadata not loaded"}

        # 统计信息
        subject_ids = set(row['Subject ID'] for row in self.metadata)
        study_uids = set(row['Study UID'] for row in self.metadata)

        # 统计模态
        modalities = defaultdict(int)
        for row in self.metadata:
            modalities[row['Modality']] += 1

        # 统计制造商
        manufacturers = defaultdict(int)
        for row in self.metadata:
            manufacturers[row['Manufacturer']] += 1

        # 日期范围
        dates = [row['Study Date'] for row in self.metadata if row['Study Date']]

        stats = {
            "total_patients": len(subject_ids),
            "total_studies": len(study_uids),
            "total_series": len(self.metadata),
            "modalities": dict(modalities),
            "manufacturers": dict(manufacturers),
            "date_range": {
                "earliest": min(dates) if dates else "N/A",
                "latest": max(dates) if dates else "N/A"
            }
        }
        return stats

    def get_series_images(self, series_uid):
        """获取特定序列的图像信息"""
        if not self.metadata:
            return {"images": [], "series_uid": series_uid}

        # 查找序列数据
        series_data = [row for row in self.metadata if row['Series UID'] == series_uid]

        if not series_data:
            return {"images": [], "series_uid": series_uid}

        series_info = series_data[0]
        file_location = series_info['File Location']

        # 构建完整路径
        full_path = DICOM_DATA_PATH / file_location.replace('./', '')

        # 使用DICOM处理器获取详细信息
        dicom_files = self.dicom_processor.get_series_dicom_files(full_path)

        # 为每个文件添加索引和缩略图URL
        images = []
        for i, dicom_file in enumerate(dicom_files):
            image_info = {
                'image_index': i,
                'file_name': dicom_file['file_name'],
                'file_path': dicom_file['file_path'],
                'file_size': dicom_file['file_size'],
                'is_dicom': dicom_file.get('is_dicom', True),
                'thumbnail_url': f"/api/dicom/thumbnail/{dicom_file['file_path']}",
                'image_url': f"/api/dicom/image/{dicom_file['file_path']}"
            }

            # 如果是DICOM文件，添加DICOM特定信息
            if dicom_file.get('is_dicom', True):
                image_info.update({
                    'instance_number': dicom_file.get('instance_number', i),
                    'rows': dicom_file.get('rows', 0),
                    'columns': dicom_file.get('columns', 0),
                    'pixel_spacing': dicom_file.get('pixel_spacing', [1.0, 1.0]),
                    'slice_thickness': dicom_file.get('slice_thickness', 1.0),
                    'window_center': dicom_file.get('window_center'),
                    'window_width': dicom_file.get('window_width'),
                    'has_pixel_data': dicom_file.get('has_pixel_data', False)
                })

            images.append(image_info)

        return {
            "images": images,
            "series_uid": series_uid,
            "total_images": len(images),
            "series_info": {
                "modality": series_info['Modality'],
                "description": series_info['Series Description'],
                "manufacturer": series_info['Manufacturer']
            }
        }

    def get_analysis_overview(self):
        """获取数据分析概览"""
        if not self.metadata:
            return {"error": "Metadata not loaded"}

        # 按患者分组统计图像数量
        patients_images = defaultdict(int)
        for row in self.metadata:
            patients_images[row['Subject ID']] += int(row['Number of Images']) if row['Number of Images'].isdigit() else 0

        images_counts = list(patients_images.values())
        images_counts.sort()

        # 计算中位数
        n = len(images_counts)
        median = images_counts[n//2] if n % 2 == 1 else (images_counts[n//2-1] + images_counts[n//2]) / 2

        # 统计模态分布
        modalities = defaultdict(int)
        for row in self.metadata:
            modalities[row['Modality']] += 1

        # 统计制造商分布
        manufacturers = defaultdict(int)
        for row in self.metadata:
            manufacturers[row['Manufacturer']] += 1

        # 统计年份分布
        years = defaultdict(int)
        for row in self.metadata:
            date = row['Study Date']
            if date and len(date) >= 4:
                try:
                    if '-' in date:
                        year = date.split('-')[2] if date.count('-') == 2 else date.split('-')[0]
                    else:
                        year = date[:4]
                    years[year] += 1
                except:
                    pass

        # 文件大小统计
        total_size_bytes = 0
        series_sizes = []
        for row in self.metadata:
            size_str = row['File Size']
            try:
                if 'MB' in size_str:
                    size_mb = float(size_str.replace(' MB', ''))
                    total_size_bytes += size_mb * 1024 * 1024
                    series_sizes.append(size_mb)
                elif 'KB' in size_str:
                    size_kb = float(size_str.replace(' KB', ''))
                    total_size_bytes += size_kb * 1024
                    series_sizes.append(size_kb / 1024)
                elif 'GB' in size_str:
                    size_gb = float(size_str.replace(' GB', ''))
                    total_size_bytes += size_gb * 1024 * 1024 * 1024
                    series_sizes.append(size_gb * 1024)
            except:
                pass

        analysis = {
            "modality_distribution": dict(modalities),
            "manufacturer_distribution": dict(manufacturers),
            "images_per_patient": {
                "min": min(images_counts) if images_counts else 0,
                "max": max(images_counts) if images_counts else 0,
                "avg": sum(images_counts) / len(images_counts) if images_counts else 0,
                "median": median
            },
            "study_dates_distribution": dict(years),
            "file_size_stats": {
                "total_size_gb": total_size_bytes / (1024 * 1024 * 1024),
                "avg_size_mb": sum(series_sizes) / len(series_sizes) if series_sizes else 0,
                "largest_series_mb": max(series_sizes) if series_sizes else 0
            }
        }

        return analysis

    def get_dicom_image(self, file_path: str, query_string: str = ""):
        """获取DICOM图像数据"""
        try:
            # 解析查询参数
            query_params = parse_qs(query_string)
            window_center = None
            window_width = None

            if 'wc' in query_params:
                window_center = float(query_params['wc'][0])
            if 'ww' in query_params:
                window_width = float(query_params['ww'][0])

            # 构建完整文件路径
            full_file_path = DICOM_DATA_PATH / file_path

            if not full_file_path.exists():
                return {"error": "File not found"}

            # 获取图像数据
            image_data = self.dicom_processor.get_dicom_image_data(
                full_file_path, window_center, window_width
            )

            if image_data:
                return {"image_data": image_data, "success": True}
            else:
                return {"error": "Failed to process DICOM image"}

        except Exception as e:
            return {"error": f"Error processing DICOM image: {str(e)}"}

    def get_dicom_thumbnail(self, file_path: str):
        """获取DICOM缩略图"""
        try:
            # 构建完整文件路径
            full_file_path = DICOM_DATA_PATH / file_path

            if not full_file_path.exists():
                return {"error": "File not found"}

            # 获取缩略图数据
            thumbnail_data = self.dicom_processor.get_dicom_thumbnail(full_file_path)

            if thumbnail_data:
                return {"thumbnail_data": thumbnail_data, "success": True}
            else:
                return {"error": "Failed to generate thumbnail"}

        except Exception as e:
            return {"error": f"Error generating thumbnail: {str(e)}"}

if __name__ == "__main__":
    PORT = 8000

    # 创建处理器实例
    handler = DICOMHandler

    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"DICOM API Server running on http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")
