from pathlib import Path
import os

print("Current working directory:", os.getcwd())

# 数据路径配置
DICOM_DATA_PATH = Path("../sourcedata/a/manifest-1603198545583")
METADATA_FILE = DICOM_DATA_PATH / "metadata.csv"

print(f"Looking for metadata file at: {METADATA_FILE.absolute()}")
print(f"File exists: {METADATA_FILE.exists()}")

# 尝试不同的路径
alt_path = Path("../sourcedata")
print(f"Sourcedata directory: {alt_path.absolute()}")
print(f"Sourcedata exists: {alt_path.exists()}")

if alt_path.exists():
    print("Contents of sourcedata:")
    for item in alt_path.iterdir():
        print(f"  {item}")
