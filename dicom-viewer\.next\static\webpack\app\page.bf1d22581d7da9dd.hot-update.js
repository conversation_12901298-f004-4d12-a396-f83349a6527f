"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPatient, setSelectedPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studies, setStudies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedStudy, setSelectedStudy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [series, setSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSeries, setSelectedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [seriesImages, setSeriesImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageViewer, setShowImageViewer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE = 'http://localhost:8000';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchPatients();\n            fetchStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const fetchPatients = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients\"));\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const data = await response.json();\n            setPatients(data.patients);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/metadata/stats\"));\n            if (!response.ok) throw new Error('Failed to fetch stats');\n            const data = await response.json();\n            setStats(data);\n        } catch (err) {\n            console.error('Failed to fetch stats:', err);\n        }\n    };\n    const fetchStudies = async (subjectId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/patients/\").concat(subjectId, \"/studies\"));\n            if (!response.ok) throw new Error('Failed to fetch studies');\n            const data = await response.json();\n            setStudies(data.studies);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchSeries = async (studyUid)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE, \"/api/studies/\").concat(studyUid, \"/series\"));\n            if (!response.ok) throw new Error('Failed to fetch series');\n            const data = await response.json();\n            setSeries(data.series);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const handlePatientSelect = (patient)=>{\n        setSelectedPatient(patient);\n        setSelectedStudy(null);\n        setSeries([]);\n        fetchStudies(patient.subject_id);\n    };\n    const handleStudySelect = (study)=>{\n        setSelectedStudy(study);\n        fetchSeries(study.study_uid);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading DICOM data...\"\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl text-red-600\",\n                children: [\n                    \"Error: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"DICOM Viewer\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Patients: \",\n                                            stats.total_patients\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Studies: \",\n                                            stats.total_studies\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Series: \",\n                                            stats.total_series\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"Patients (\",\n                                            patients.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.subject_id) === patient.subject_id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handlePatientSelect(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: patient.subject_id\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            patient.study_count,\n                                                            \" studies, \",\n                                                            patient.series_count,\n                                                            \" series\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: patient.modalities.join(', ')\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, patient.subject_id, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"Studies \",\n                                            selectedPatient && \"(\".concat(selectedPatient.subject_id, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedPatient ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: studies.map((study)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat((selectedStudy === null || selectedStudy === void 0 ? void 0 : selectedStudy.study_uid) === study.study_uid ? 'bg-green-100 border-green-300' : 'bg-gray-50 hover:bg-gray-100'),\n                                                onClick: ()=>handleStudySelect(study),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: study.study_date\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: study.study_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            study.series_count,\n                                                            \" series - \",\n                                                            study.modalities.join(', ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, study.study_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"Select a patient to view studies\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: [\n                                            \"Series \",\n                                            selectedStudy && \"(\".concat(selectedStudy.study_date, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedStudy ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: series.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: s.modality\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: s.series_description\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            s.number_of_images,\n                                                            \" images - \",\n                                                            s.file_size\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: s.manufacturer\n                                                    }, void 0, false, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, s.series_uid, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"Select a study to view series\"\n                                    }, void 0, false, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Dataset Statistics\"\n                            }, void 0, false, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"Modalities\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.modalities).map((param)=>{\n                                                    let [modality, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: modality\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, modality, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"Manufacturers\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: Object.entries(stats.manufacturers).slice(0, 5).map((param)=>{\n                                                    let [manufacturer, count] = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: manufacturer\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: count\n                                                            }, void 0, false, {\n                                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, manufacturer, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"Date Range\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"From: \",\n                                                            stats.date_range.earliest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"To: \",\n                                                            stats.date_range.latest\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-700 mb-2\",\n                                                children: \"Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_patients,\n                                                            \" Patients\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_studies,\n                                                            \" Studies\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.total_series,\n                                                            \" Series\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"I:\\\\DICOM\\\\dicom-viewer\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"hWm118uS6mcbmgQzTnUmhtIzXGo=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});