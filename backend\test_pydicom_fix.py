#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PyDicom模拟器的问题
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from pydicom_reader import read_dicom_with_pydicom
    print("✅ 成功导入PyDicom读取器")
    
    # 测试读取DICOM文件
    test_file = "../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/09-18-2008-StudyID-NA-69331/3.000000-NA-78236/1-1.dcm"
    
    if os.path.exists(test_file):
        print(f"📖 测试文件存在: {test_file}")
        
        try:
            result = read_dicom_with_pydicom(test_file, 40, 400)
            print(f"✅ 测试成功: {len(result)} 字符")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"⚠️ 测试文件不存在: {test_file}")
        
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
