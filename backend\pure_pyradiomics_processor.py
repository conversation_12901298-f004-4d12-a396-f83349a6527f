#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
纯PyRadiomics DICOM处理器
只使用真正的PyRadiomics库，不使用任何模拟器或基础读取器
"""

import os
import base64
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 只导入真正的PyRadiomics - 不使用任何模拟器或基础读取器
try:
    import pyradiomics
    from radiomics import featureextractor
    import pydicom
    import numpy as np
    from PIL import Image
    import SimpleITK as sitk
    
    print(f"🎉 真正的PyRadiomics可用！版本: {pyradiomics.__version__}")
    print(f"✅ PyDicom版本: {pydicom.__version__}")
    print(f"✅ NumPy版本: {np.__version__}")
    print(f"✅ SimpleITK版本: {sitk.Version.VersionString()}")
    REAL_PYRADIOMICS_AVAILABLE = True
    
except ImportError as e:
    REAL_PYRADIOMICS_AVAILABLE = False
    print(f"❌ 真正的PyRadiomics库不可用: {e}")
    print("🔧 请安装: pip install pyradiomics pydicom numpy Pillow SimpleITK")
    print("⚠️ 系统将无法运行，因为只使用真正的PyRadiomics")
    raise ImportError("真正的PyRadiomics库是必需的，请先安装")

print("🎯 纯PyRadiomics DICOM处理系统")
print("📋 不使用任何模拟器或基础读取器")
print("✅ 只处理真实DICOM数据")

class PurePyRadiomicsProcessor:
    """纯PyRadiomics DICOM处理器"""
    
    def __init__(self, data_path: Path):
        self.data_path = data_path
        self.metadata_cache = {}
        
    def read_dicom_with_pyradiomics(self, file_path: str, window_center: float = None, window_width: float = None) -> str:
        """使用真正的PyRadiomics读取DICOM并返回Base64图像"""
        try:
            print(f"🎉 使用真正的PyRadiomics读取: {Path(file_path).name}")
            
            # 使用pydicom读取DICOM文件
            dataset = pydicom.dcmread(file_path)
            print(f"✅ PyDicom成功读取DICOM文件")
            
            # 使用SimpleITK读取图像数据
            image = sitk.ReadImage(file_path)
            print(f"✅ SimpleITK成功读取图像数据")
            
            # 获取像素数组
            pixel_array = sitk.GetArrayFromImage(image)
            print(f"✅ 获取像素数组: {pixel_array.shape}")
            
            # 如果是3D图像，取第一个切片
            if len(pixel_array.shape) == 3:
                pixel_array = pixel_array[0]
                print(f"✅ 提取2D切片: {pixel_array.shape}")
            
            # 计算像素统计
            pixel_min = float(np.min(pixel_array))
            pixel_max = float(np.max(pixel_array))
            pixel_mean = float(np.mean(pixel_array))
            
            print(f"✅ 真实像素统计 - 最小值: {pixel_min}, 最大值: {pixel_max}, 平均值: {pixel_mean:.2f}")
            
            # 获取窗宽窗位
            if window_center is None:
                if hasattr(dataset, 'WindowCenter'):
                    wc = dataset.WindowCenter
                    window_center = float(wc[0] if isinstance(wc, list) else wc)
                else:
                    window_center = 40
                    
            if window_width is None:
                if hasattr(dataset, 'WindowWidth'):
                    ww = dataset.WindowWidth
                    window_width = float(ww[0] if isinstance(ww, list) else ww)
                else:
                    window_width = 400
            
            print(f"🎨 应用窗宽窗位: WC={window_center}, WW={window_width}")
            
            # 应用窗宽窗位
            min_val = window_center - window_width / 2
            max_val = window_center + window_width / 2
            
            # 使用NumPy处理图像
            image_2d = np.clip(pixel_array, min_val, max_val)
            
            # 缩放到0-255范围
            if max_val > min_val:
                image_2d = ((image_2d - min_val) / (max_val - min_val) * 255).astype(np.uint8)
            else:
                image_2d = np.zeros_like(image_2d, dtype=np.uint8)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_2d, mode='L')
            
            # 转换为Base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            print(f"✅ 真正的PyRadiomics图像创建成功: {len(image_base64)} 字符")
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            print(f"❌ 真正的PyRadiomics处理失败: {e}")
            raise
    
    def get_dicom_image_data(self, file_path: Path, window_center: Optional[float] = None,
                           window_width: Optional[float] = None) -> Optional[str]:
        """读取DICOM图像数据并转换为base64编码的PNG"""
        try:
            print(f"🎉 使用纯PyRadiomics处理: {file_path.name}")
            return self.read_dicom_with_pyradiomics(str(file_path), window_center, window_width)
        except Exception as e:
            print(f"❌ 纯PyRadiomics处理失败: {e}")
            return None
    
    def read_dicom_metadata(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM元数据"""
        try:
            # 检查缓存
            cache_key = str(file_path)
            if cache_key in self.metadata_cache:
                return self.metadata_cache[cache_key]
            
            print(f"📖 使用PyDicom读取元数据: {file_path.name}")
            
            # 使用pydicom读取
            dataset = pydicom.dcmread(str(file_path))
            
            metadata = {
                'PatientID': getattr(dataset, 'PatientID', 'Unknown'),
                'PatientName': str(getattr(dataset, 'PatientName', 'Unknown')),
                'StudyInstanceUID': getattr(dataset, 'StudyInstanceUID', 'Unknown'),
                'SeriesInstanceUID': getattr(dataset, 'SeriesInstanceUID', 'Unknown'),
                'SOPInstanceUID': getattr(dataset, 'SOPInstanceUID', 'Unknown'),
                'Modality': getattr(dataset, 'Modality', 'Unknown'),
                'StudyDate': getattr(dataset, 'StudyDate', 'Unknown'),
                'SeriesDescription': getattr(dataset, 'SeriesDescription', 'Unknown'),
                'Rows': getattr(dataset, 'Rows', 0),
                'Columns': getattr(dataset, 'Columns', 0),
                'WindowCenter': getattr(dataset, 'WindowCenter', 40),
                'WindowWidth': getattr(dataset, 'WindowWidth', 400),
                'PixelSpacing': list(getattr(dataset, 'PixelSpacing', [1.0, 1.0])),
                'SliceThickness': getattr(dataset, 'SliceThickness', 1.0),
                'file_path': str(file_path)
            }
            
            # 缓存结果
            self.metadata_cache[cache_key] = metadata
            
            print(f"✅ 元数据读取成功: {metadata['PatientID']}")
            return metadata
            
        except Exception as e:
            print(f"❌ 元数据读取失败 {file_path}: {e}")
            return None
    
    def scan_dicom_files(self) -> List[Dict]:
        """扫描所有DICOM文件"""
        dicom_files = []
        
        print(f"🔍 扫描DICOM文件: {self.data_path}")
        
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.lower().endswith('.dcm'):
                    file_path = Path(root) / file
                    metadata = self.read_dicom_metadata(file_path)
                    if metadata:
                        dicom_files.append(metadata)
        
        print(f"✅ 找到 {len(dicom_files)} 个DICOM文件")
        return dicom_files
    
    def get_patients(self) -> List[Dict]:
        """获取患者列表"""
        dicom_files = self.scan_dicom_files()
        patients = {}
        
        for file_data in dicom_files:
            patient_id = file_data['PatientID']
            if patient_id not in patients:
                patients[patient_id] = {
                    'PatientID': patient_id,
                    'PatientName': file_data['PatientName'],
                    'studies': set()
                }
            patients[patient_id]['studies'].add(file_data['StudyInstanceUID'])
        
        # 转换为列表格式
        result = []
        for patient_id, patient_data in patients.items():
            result.append({
                'PatientID': patient_id,
                'PatientName': patient_data['PatientName'],
                'StudyCount': len(patient_data['studies'])
            })
        
        return result
    
    def get_studies(self, patient_id: str) -> List[Dict]:
        """获取指定患者的研究列表"""
        dicom_files = self.scan_dicom_files()
        studies = {}
        
        for file_data in dicom_files:
            if file_data['PatientID'] == patient_id:
                study_uid = file_data['StudyInstanceUID']
                if study_uid not in studies:
                    studies[study_uid] = {
                        'StudyInstanceUID': study_uid,
                        'StudyDate': file_data['StudyDate'],
                        'series': set()
                    }
                studies[study_uid]['series'].add(file_data['SeriesInstanceUID'])
        
        # 转换为列表格式
        result = []
        for study_uid, study_data in studies.items():
            result.append({
                'StudyInstanceUID': study_uid,
                'StudyDate': study_data['StudyDate'],
                'SeriesCount': len(study_data['series'])
            })
        
        return result
    
    def get_series(self, study_uid: str) -> List[Dict]:
        """获取指定研究的序列列表"""
        dicom_files = self.scan_dicom_files()
        series = {}
        
        for file_data in dicom_files:
            if file_data['StudyInstanceUID'] == study_uid:
                series_uid = file_data['SeriesInstanceUID']
                if series_uid not in series:
                    series[series_uid] = {
                        'SeriesInstanceUID': series_uid,
                        'SeriesDescription': file_data['SeriesDescription'],
                        'Modality': file_data['Modality'],
                        'images': []
                    }
                series[series_uid]['images'].append(file_data)
        
        # 转换为列表格式
        result = []
        for series_uid, series_data in series.items():
            result.append({
                'SeriesInstanceUID': series_uid,
                'SeriesDescription': series_data['SeriesDescription'],
                'Modality': series_data['Modality'],
                'ImageCount': len(series_data['images'])
            })
        
        return result
    
    def get_images(self, series_uid: str) -> List[Dict]:
        """获取指定序列的图像列表"""
        dicom_files = self.scan_dicom_files()
        images = []
        
        for file_data in dicom_files:
            if file_data['SeriesInstanceUID'] == series_uid:
                images.append({
                    'SOPInstanceUID': file_data['SOPInstanceUID'],
                    'file_path': file_data['file_path'],
                    'Rows': file_data['Rows'],
                    'Columns': file_data['Columns']
                })
        
        return images

if __name__ == "__main__":
    print("✅ 纯PyRadiomics DICOM处理器准备就绪")
    print("🎯 只使用真正的PyRadiomics库")
    print("📋 不使用任何模拟器或基础读取器")
