#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import struct
from pathlib import Path
from typing import Dict, Optional, List, Tuple

class PureDicomReader:
    """纯Python DICOM读取器 - 不依赖外部库，直接读取真实DICOM数据"""

    def __init__(self, path: str):
        self.path = Path(path)
        self.pixel_data = None
        self.metadata = {}

    def read_dicom(self) -> Dict:
        """读取真实DICOM文件"""
        try:
            print(f"🔍 纯Python DICOM读取器处理: {self.path.name}")

            with open(self.path, 'rb') as f:
                data = f.read()

            # 查找DICM标识
            dicm_pos = data.find(b'DICM')
            if dicm_pos == -1:
                raise ValueError("不是有效的DICOM文件")

            print(f"✅ 找到DICM标识在位置: {dicm_pos}")

            # 解析DICOM元数据
            self._parse_metadata(data, dicm_pos + 4)

            # 提取像素数据
            pixel_array = self._extract_pixel_data(data, dicm_pos + 4)

            if pixel_array is None:
                raise ValueError("未找到像素数据")

            self.pixel_data = pixel_array

            # 获取图像尺寸
            rows = self.metadata.get('Rows', 512)
            cols = self.metadata.get('Columns', 512)

            print(f"✅ 纯Python读取成功: {rows}x{cols}, 像素数: {len(pixel_array)}")

            return {
                'success': True,
                'reader': 'PureDicom',
                'image_shape': (rows, cols),
                'pixel_count': len(pixel_array),
                'pixel_range': [min(pixel_array), max(pixel_array)],
                'metadata': self.metadata
            }

        except Exception as e:
            print(f"❌ 纯Python DICOM读取失败: {e}")
            raise

    def _parse_metadata(self, data: bytes, start_pos: int):
        """解析DICOM元数据"""
        pos = start_pos

        while pos < len(data) - 8:
            try:
                # 读取标签 (group, element)
                group = struct.unpack('<H', data[pos:pos+2])[0]
                element = struct.unpack('<H', data[pos+2:pos+4])[0]

                # 跳过像素数据标签，稍后单独处理
                if group == 0x7FE0 and element == 0x0010:
                    break

                # 读取VR (Value Representation)
                vr = data[pos+4:pos+6].decode('ascii', errors='ignore')

                # 根据VR确定长度字段的位置和大小
                if vr in ['OB', 'OW', 'OF', 'SQ', 'UT', 'UN']:
                    # 显式VR，长度为4字节
                    length = struct.unpack('<I', data[pos+8:pos+12])[0]
                    value_pos = pos + 12
                elif len(vr) == 2 and vr.isalpha():
                    # 显式VR，长度为2字节
                    length = struct.unpack('<H', data[pos+6:pos+8])[0]
                    value_pos = pos + 8
                else:
                    # 隐式VR，长度为4字节
                    length = struct.unpack('<I', data[pos+4:pos+8])[0]
                    value_pos = pos + 8

                # 提取特定的元数据
                if group == 0x0028:  # Image Information
                    if element == 0x0010:  # Rows
                        if length == 2:
                            self.metadata['Rows'] = struct.unpack('<H', data[value_pos:value_pos+2])[0]
                    elif element == 0x0011:  # Columns
                        if length == 2:
                            self.metadata['Columns'] = struct.unpack('<H', data[value_pos:value_pos+2])[0]
                    elif element == 0x0100:  # Bits Allocated
                        if length == 2:
                            self.metadata['BitsAllocated'] = struct.unpack('<H', data[value_pos:value_pos+2])[0]
                    elif element == 0x1050:  # Window Center
                        if length > 0:
                            try:
                                wc_str = data[value_pos:value_pos+length].decode('ascii', errors='ignore').strip()
                                self.metadata['WindowCenter'] = float(wc_str.split('\\')[0])
                            except:
                                pass
                    elif element == 0x1051:  # Window Width
                        if length > 0:
                            try:
                                ww_str = data[value_pos:value_pos+length].decode('ascii', errors='ignore').strip()
                                self.metadata['WindowWidth'] = float(ww_str.split('\\')[0])
                            except:
                                pass

                # 移动到下一个标签
                pos = value_pos + length

                # 对齐到偶数字节
                if pos % 2 == 1:
                    pos += 1

            except:
                pos += 2  # 如果解析失败，移动2字节继续

            # 防止无限循环
            if pos >= len(data) - 8:
                break

    def _extract_pixel_data(self, data: bytes, start_pos: int) -> Optional[List[int]]:
        """提取像素数据"""
        # 多种方式查找像素数据标签 (7FE0,0010)

        # 方法1: 小端序
        pixel_tag1 = b'\xe0\x7f\x10\x00'
        pos1 = data.find(pixel_tag1, start_pos)

        # 方法2: 大端序
        pixel_tag2 = b'\x7f\xe0\x00\x10'
        pos2 = data.find(pixel_tag2, start_pos)

        # 方法3: 查找文本形式
        pixel_tag3 = b'7FE0'
        pos3 = data.find(pixel_tag3, start_pos)

        # 选择找到的第一个位置
        positions = [p for p in [pos1, pos2, pos3] if p != -1]

        if not positions:
            print("⚠️ 未找到像素数据标签，尝试启发式搜索")
            return self._heuristic_pixel_search(data, start_pos)

        pos = min(positions)
        print(f"✅ 找到像素数据标签在位置: {pos}")

        # 跳过标签，读取长度
        try:
            # 尝试读取VR
            vr_pos = pos + 4
            vr = data[vr_pos:vr_pos+2]

            if vr == b'OW' or vr == b'OB':
                # 显式VR
                length = struct.unpack('<I', data[vr_pos+4:vr_pos+8])[0]
                pixel_start = vr_pos + 8
            else:
                # 隐式VR
                length = struct.unpack('<I', data[vr_pos:vr_pos+4])[0]
                pixel_start = vr_pos + 4

            print(f"✅ 像素数据长度: {length} 字节")

            if length == 0xFFFFFFFF:
                # 未定义长度，查找序列结束
                print("⚠️ 未定义长度的像素数据")
                return None

            # 提取像素数据
            pixel_bytes = data[pixel_start:pixel_start+length]

            # 根据位深度解析像素
            bits_allocated = self.metadata.get('BitsAllocated', 16)

            if bits_allocated == 16:
                # 16位像素
                pixel_count = len(pixel_bytes) // 2
                pixels = []
                for i in range(0, len(pixel_bytes), 2):
                    if i + 1 < len(pixel_bytes):
                        pixel = struct.unpack('<H', pixel_bytes[i:i+2])[0]
                        pixels.append(pixel)

                print(f"✅ 解析16位像素: {len(pixels)} 个")
                return pixels

            elif bits_allocated == 8:
                # 8位像素
                pixels = list(pixel_bytes)
                print(f"✅ 解析8位像素: {len(pixels)} 个")
                return pixels

            else:
                print(f"⚠️ 不支持的位深度: {bits_allocated}")
                return None

        except Exception as e:
            print(f"❌ 像素数据解析失败: {e}")
            return None

    def _heuristic_pixel_search(self, data: bytes, start_pos: int) -> Optional[List[int]]:
        """启发式像素数据搜索"""
        try:
            print("🔍 启发式搜索像素数据...")

            # 查找大块连续数据（可能是像素数据）
            chunk_size = 512 * 512 * 2  # 假设512x512图像，16位

            for i in range(start_pos, len(data) - chunk_size, 1024):
                chunk = data[i:i+chunk_size]

                # 检查是否像像素数据（有一定的变化）
                if len(set(chunk[::100])) > 10:  # 每100字节采样，检查变化
                    print(f"✅ 找到可能的像素数据在位置: {i}")

                    # 尝试解析为16位数据
                    try:
                        pixels = []
                        for j in range(0, min(len(chunk), 512*512*2), 2):
                            if j+1 < len(chunk):
                                val = struct.unpack('<H', chunk[j:j+2])[0]
                                pixels.append(val)

                        if len(pixels) >= 512*512:
                            pixels = pixels[:512*512]
                            print(f"✅ 启发式搜索成功提取 {len(pixels)} 个像素值")
                            return pixels
                    except:
                        continue

            print("⚠️ 启发式搜索未找到有效像素数据")
            return None

        except Exception as e:
            print(f"❌ 启发式搜索失败: {e}")
            return None

    def create_image_base64(self, window_center: float = None, window_width: float = None) -> str:
        """创建Base64编码的图像"""
        if self.pixel_data is None:
            raise ValueError("没有像素数据")

        try:
            rows = self.metadata.get('Rows', 512)
            cols = self.metadata.get('Columns', 512)

            print(f"🎨 创建图像: {rows}x{cols}")

            # 确保有足够的像素数据
            expected_pixels = rows * cols
            if len(self.pixel_data) < expected_pixels:
                print(f"⚠️ 像素数据不足: 需要{expected_pixels}, 实际{len(self.pixel_data)}")
                # 填充不足的像素
                self.pixel_data.extend([0] * (expected_pixels - len(self.pixel_data)))

            # 应用窗宽窗位
            if window_center is None:
                window_center = self.metadata.get('WindowCenter', 40)
            if window_width is None:
                window_width = self.metadata.get('WindowWidth', 400)

            print(f"🎨 应用窗宽窗位: WC={window_center}, WW={window_width}")

            # 计算窗口范围
            min_val = window_center - window_width / 2
            max_val = window_center + window_width / 2

            # 转换像素值到0-255范围
            image_data = []
            for pixel in self.pixel_data[:expected_pixels]:
                if pixel < min_val:
                    gray = 0
                elif pixel > max_val:
                    gray = 255
                else:
                    gray = int((pixel - min_val) / (max_val - min_val) * 255)
                image_data.append(gray)

            # 创建简单的PNG格式图像
            return self._create_png_base64(image_data, cols, rows)

        except Exception as e:
            print(f"❌ 图像创建失败: {e}")
            raise

    def _create_png_base64(self, pixels: List[int], width: int, height: int) -> str:
        """创建PNG格式的Base64图像"""
        try:
            # 简化版PNG创建 - 使用原始像素数据
            # 这里我们创建一个简单的灰度图像

            # 创建BMP格式（更简单）
            # BMP文件头
            file_size = 54 + width * height  # 文件头 + 像素数据
            bmp_header = struct.pack('<2sIHHI', b'BM', file_size, 0, 0, 54)

            # BMP信息头
            info_header = struct.pack('<IIIHHIIIIII',
                40,  # 信息头大小
                width, height,  # 宽度和高度
                1, 8,  # 平面数和位深度
                0,  # 压缩方式
                width * height,  # 图像大小
                0, 0, 256, 0  # 其他参数
            )

            # 调色板（256色灰度）
            palette = b''
            for i in range(256):
                palette += struct.pack('<BBB', i, i, i) + b'\x00'

            # 像素数据（BMP是从下到上存储的）
            pixel_data = b''
            for y in range(height-1, -1, -1):
                for x in range(width):
                    idx = y * width + x
                    if idx < len(pixels):
                        pixel_data += struct.pack('<B', pixels[idx])
                    else:
                        pixel_data += b'\x00'

            # 组合BMP文件
            bmp_data = bmp_header + info_header + palette + pixel_data

            # 转换为Base64
            image_base64 = base64.b64encode(bmp_data).decode('utf-8')

            print(f"✅ 纯Python图像创建成功: {len(image_base64)} 字符")
            return f"data:image/bmp;base64,{image_base64}"

        except Exception as e:
            print(f"❌ PNG创建失败: {e}")
            raise

def read_dicom_with_pure_reader(file_path: str, window_center: float = None, window_width: float = None) -> str:
    """使用纯Python读取器读取DICOM并返回Base64图像"""
    try:
        print(f"🔍 启动纯Python DICOM读取器")

        reader = PureDicomReader(file_path)
        info = reader.read_dicom()

        print(f"✅ 纯Python读取成功:")
        print(f"   - 图像形状: {info['image_shape']}")
        print(f"   - 像素数量: {info['pixel_count']}")
        print(f"   - 像素范围: {info['pixel_range']}")

        # 创建图像
        image_base64 = reader.create_image_base64(window_center, window_width)

        return image_base64

    except Exception as e:
        print(f"❌ 纯Python处理失败: {e}")
        raise

if __name__ == "__main__":
    print("🔍 纯Python DICOM读取器准备就绪")
    print("✅ 不依赖任何外部库，直接读取真实DICOM数据")
