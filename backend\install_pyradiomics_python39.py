#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyRadiomics Python 3.9 安装指南
"""

import sys
import subprocess
import os

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    major = sys.version_info.major
    minor = sys.version_info.minor
    micro = sys.version_info.micro
    
    print(f"当前Python版本: {major}.{minor}.{micro}")
    
    if major == 3 and 7 <= minor <= 9:
        print("✅ Python版本兼容PyRadiomics")
        return True
    else:
        print("❌ Python版本不兼容PyRadiomics")
        print("PyRadiomics需要Python 3.7-3.9")
        return False

def install_pyradiomics():
    """安装PyRadiomics"""
    print("\n🔧 开始安装PyRadiomics...")
    
    packages = [
        "pyradiomics",
        "pydicom", 
        "numpy",
        "Pillow",
        "SimpleITK"
    ]
    
    for package in packages:
        print(f"📦 安装 {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def test_installation():
    """测试安装"""
    print("\n🧪 测试PyRadiomics安装...")
    
    try:
        import pyradiomics
        print(f"✅ PyRadiomics版本: {pyradiomics.__version__}")
        
        import pydicom
        print(f"✅ PyDicom版本: {pydicom.__version__}")
        
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
        
        from PIL import Image
        print(f"✅ Pillow可用")
        
        import SimpleITK as sitk
        print(f"✅ SimpleITK版本: {sitk.Version.VersionString()}")
        
        from radiomics import featureextractor
        print("✅ PyRadiomics功能模块可用")
        
        print("\n🎉 所有库安装成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 PyRadiomics Python 3.9 安装指南")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        print("\n💡 解决方案:")
        print("1. 下载Python 3.9: https://www.python.org/downloads/release/python-3918/")
        print("2. 或使用conda: conda create -n pyradiomics python=3.9")
        print("3. 或使用pyenv: pyenv install 3.9.18")
        return
    
    # 测试是否已安装
    if test_installation():
        print("\n🎉 PyRadiomics已经安装完成！")
        print("可以直接运行: python pure_pyradiomics_server.py")
        return
    
    # 安装PyRadiomics
    if install_pyradiomics():
        print("\n🎉 安装完成！")
        
        # 再次测试
        if test_installation():
            print("\n✅ 验证成功！现在可以运行纯PyRadiomics服务器:")
            print("python pure_pyradiomics_server.py")
        else:
            print("\n⚠️ 安装完成但验证失败，请检查安装")
    else:
        print("\n❌ 安装失败，请手动安装:")
        print("pip install pyradiomics pydicom numpy Pillow SimpleITK")

if __name__ == "__main__":
    main()
