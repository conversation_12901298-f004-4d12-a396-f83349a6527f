#!/usr/bin/env python3
"""基于PyRadiomics的DICOM读取器"""

import base64
import numpy as np
from pathlib import Path
from typing import Dict, Optional, Tuple, List
import logging

# 配置日志
logging.getLogger('radiomics').setLevel(logging.WARNING)

try:
    import SimpleITK as sitk
    import radiomics
    from radiomics import featureextractor
    PYRADIOMICS_AVAILABLE = True
    print("✅ PyRadiomics libraries loaded successfully!")
except ImportError as e:
    PYRADIOMICS_AVAILABLE = False
    print(f"⚠️ Warning: PyRadiomics not available ({e})")

class PyRadiomicsDICOMReader:
    """基于PyRadiomics的DICOM读取器"""
    
    def __init__(self):
        self.available = PYRADIOMICS_AVAILABLE
        if self.available:
            # 初始化特征提取器
            self.extractor = featureextractor.RadiomicsFeatureExtractor()
            self.extractor.enableAllImageTypes()
            self.extractor.enableAllFeatures()
    
    def read_dicom_image(self, file_path: Path) -> Optional[Dict]:
        """读取DICOM图像并返回详细信息"""
        if not self.available:
            return None
        
        try:
            # 使用SimpleITK读取DICOM文件
            image = sitk.ReadImage(str(file_path))
            
            # 获取图像基本信息
            size = image.GetSize()
            spacing = image.GetSpacing()
            origin = image.GetOrigin()
            direction = image.GetDirection()
            
            # 获取像素数组
            pixel_array = sitk.GetArrayFromImage(image)
            
            # 获取DICOM元数据
            metadata = self._extract_dicom_metadata(image)
            
            return {
                'file_path': str(file_path),
                'image_size': size,  # (width, height, depth)
                'pixel_spacing': spacing,
                'origin': origin,
                'direction': direction,
                'pixel_array': pixel_array,
                'pixel_type': image.GetPixelIDTypeAsString(),
                'number_of_components': image.GetNumberOfComponentsPerPixel(),
                'metadata': metadata,
                'min_value': float(np.min(pixel_array)),
                'max_value': float(np.max(pixel_array)),
                'mean_value': float(np.mean(pixel_array)),
                'std_value': float(np.std(pixel_array))
            }
            
        except Exception as e:
            print(f"Error reading DICOM with PyRadiomics: {e}")
            return None
    
    def _extract_dicom_metadata(self, image: 'sitk.Image') -> Dict:
        """提取DICOM元数据"""
        metadata = {}
        
        # 常用的DICOM标签
        dicom_tags = {
            '0010|0020': 'PatientID',
            '0010|0010': 'PatientName',
            '0008|0020': 'StudyDate',
            '0008|0030': 'StudyTime',
            '0008|0060': 'Modality',
            '0008|103E': 'SeriesDescription',
            '0020|0013': 'InstanceNumber',
            '0028|0010': 'Rows',
            '0028|0011': 'Columns',
            '0028|0100': 'BitsAllocated',
            '0028|0101': 'BitsStored',
            '0028|1050': 'WindowCenter',
            '0028|1051': 'WindowWidth',
            '0018|0050': 'SliceThickness',
            '0028|0030': 'PixelSpacing'
        }
        
        for tag, name in dicom_tags.items():
            try:
                if image.HasMetaDataKey(tag):
                    value = image.GetMetaData(tag)
                    metadata[name.lower()] = value
            except:
                pass
        
        return metadata
    
    def create_image_from_array(self, pixel_array: np.ndarray, 
                               window_center: float = None, 
                               window_width: float = None,
                               slice_index: int = 0) -> str:
        """从像素数组创建图像"""
        try:
            # 如果是3D图像，选择指定切片
            if len(pixel_array.shape) == 3:
                if slice_index >= pixel_array.shape[0]:
                    slice_index = pixel_array.shape[0] // 2  # 选择中间切片
                image_slice = pixel_array[slice_index]
            else:
                image_slice = pixel_array
            
            # 确保是2D图像
            if len(image_slice.shape) > 2:
                image_slice = image_slice.squeeze()
            
            # 应用窗宽窗位
            if window_center is not None and window_width is not None:
                image_slice = self._apply_window_level(image_slice, window_center, window_width)
            else:
                # 自动调整对比度
                image_slice = self._auto_contrast(image_slice)
            
            # 转换为8位图像
            image_8bit = self._convert_to_8bit(image_slice)
            
            # 创建SVG图像
            return self._create_svg_from_array(image_8bit)
            
        except Exception as e:
            print(f"Error creating image from array: {e}")
            return self._create_error_image(str(e))
    
    def _apply_window_level(self, image: np.ndarray, center: float, width: float) -> np.ndarray:
        """应用窗宽窗位"""
        min_val = center - width / 2
        max_val = center + width / 2
        
        # 窗宽窗位变换
        windowed = np.clip(image, min_val, max_val)
        windowed = (windowed - min_val) / width
        
        return windowed
    
    def _auto_contrast(self, image: np.ndarray) -> np.ndarray:
        """自动对比度调整"""
        # 使用2%和98%分位数进行对比度拉伸
        p2, p98 = np.percentile(image, (2, 98))
        if p98 > p2:
            image = (image - p2) / (p98 - p2)
            image = np.clip(image, 0, 1)
        else:
            image = np.zeros_like(image)
        
        return image
    
    def _convert_to_8bit(self, image: np.ndarray) -> np.ndarray:
        """转换为8位图像"""
        # 确保值在0-1范围内
        if image.max() > 1 or image.min() < 0:
            image = (image - image.min()) / (image.max() - image.min())
        
        # 转换为0-255范围
        return (image * 255).astype(np.uint8)
    
    def _create_svg_from_array(self, image_array: np.ndarray) -> str:
        """从数组创建SVG图像"""
        height, width = image_array.shape
        
        # 限制显示尺寸
        max_size = 512
        if width > max_size or height > max_size:
            scale = max_size / max(width, height)
            new_width = int(width * scale)
            new_height = int(height * scale)
        else:
            new_width, new_height = width, height
        
        # 创建SVG
        svg_content = f'''<svg width="{new_width}" height="{new_height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#000"/>'''
        
        # 采样显示像素
        step_x = max(1, width // new_width)
        step_y = max(1, height // new_height)
        
        for y in range(0, height, step_y):
            for x in range(0, width, step_x):
                if y < height and x < width:
                    pixel_value = image_array[y, x]
                    color = f"#{pixel_value:02x}{pixel_value:02x}{pixel_value:02x}"
                    
                    svg_x = x * new_width // width
                    svg_y = y * new_height // height
                    size_x = max(1, step_x * new_width // width)
                    size_y = max(1, step_y * new_height // height)
                    
                    svg_content += f'<rect x="{svg_x}" y="{svg_y}" width="{size_x}" height="{size_y}" fill="{color}"/>'
        
        # 添加信息叠加
        svg_content += f'''
            <!-- 信息叠加 -->
            <rect x="10" y="10" width="200" height="80" fill="#000" opacity="0.8" rx="5"/>
            <text x="20" y="30" font-family="monospace" font-size="12" fill="#0f0">PyRadiomics DICOM</text>
            <text x="20" y="45" font-family="monospace" font-size="10" fill="#0a0">尺寸: {width}×{height}</text>
            <text x="20" y="60" font-family="monospace" font-size="10" fill="#0a0">真实医学图像</text>
            <text x="20" y="75" font-family="monospace" font-size="10" fill="#0a0">SimpleITK处理</text>
        </svg>'''
        
        # 转换为base64
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
        
        return f"data:image/svg+xml;base64,{svg_base64}"
    
    def _create_error_image(self, error_msg: str) -> str:
        """创建错误图像"""
        svg_content = f'''<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#1a1a1a"/>
            <text x="50%" y="45%" text-anchor="middle" font-family="monospace" font-size="16" fill="#f44">
                PyRadiomics错误
            </text>
            <text x="50%" y="55%" text-anchor="middle" font-family="monospace" font-size="12" fill="#f88">
                {error_msg[:50]}
            </text>
        </svg>'''
        
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
        
        return f"data:image/svg+xml;base64,{svg_base64}"
    
    def extract_radiomics_features(self, file_path: Path, mask_path: Path = None) -> Optional[Dict]:
        """提取影像组学特征"""
        if not self.available:
            return None
        
        try:
            if mask_path and mask_path.exists():
                # 使用掩膜提取特征
                features = self.extractor.execute(str(file_path), str(mask_path))
            else:
                # 创建简单的掩膜（整个图像）
                image = sitk.ReadImage(str(file_path))
                mask = sitk.BinaryThreshold(image, lowerThreshold=-1000, upperThreshold=3000)
                features = self.extractor.execute(image, mask)
            
            # 转换为普通字典
            feature_dict = {}
            for key, value in features.items():
                try:
                    if isinstance(value, (int, float)):
                        feature_dict[key] = float(value)
                    else:
                        feature_dict[key] = str(value)
                except:
                    feature_dict[key] = str(value)
            
            return feature_dict
            
        except Exception as e:
            print(f"Error extracting radiomics features: {e}")
            return None

# 测试函数
def test_pyradiomics_reader():
    """测试PyRadiomics读取器"""
    reader = PyRadiomicsDICOMReader()
    
    if not reader.available:
        print("❌ PyRadiomics不可用")
        return
    
    # 测试文件
    test_file = Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-001.dcm")
    
    if test_file.exists():
        print(f"测试文件: {test_file.name}")
        
        # 读取DICOM图像
        dicom_info = reader.read_dicom_image(test_file)
        if dicom_info:
            print(f"✅ 读取DICOM成功:")
            print(f"   图像尺寸: {dicom_info['image_size']}")
            print(f"   像素间距: {dicom_info['pixel_spacing']}")
            print(f"   像素类型: {dicom_info['pixel_type']}")
            print(f"   值范围: {dicom_info['min_value']:.1f} - {dicom_info['max_value']:.1f}")
            print(f"   平均值: {dicom_info['mean_value']:.1f}")
            
            # 生成图像
            image_data = reader.create_image_from_array(
                dicom_info['pixel_array'], 
                window_center=40, 
                window_width=400
            )
            print(f"✅ 生成图像: {len(image_data)} 字符")
            
            # 提取影像组学特征
            print("\n--- 提取影像组学特征 ---")
            features = reader.extract_radiomics_features(test_file)
            if features:
                print(f"✅ 提取特征成功: {len(features)} 个特征")
                # 显示一些关键特征
                for key, value in list(features.items())[:5]:
                    print(f"   {key}: {value}")
        else:
            print("❌ 读取DICOM失败")
    else:
        print(f"❌ 测试文件不存在: {test_file}")

if __name__ == "__main__":
    test_pyradiomics_reader()
