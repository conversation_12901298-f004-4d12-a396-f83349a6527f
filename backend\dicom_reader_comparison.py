#!/usr/bin/env python3
"""DICOM读取器对比测试工具"""

import time
import sys
from pathlib import Path
from typing import Dict, List, Optional

# 导入所有读取器
from native_dicom_reader import Native<PERSON>COMReader
from enhanced_dicom_reader import <PERSON>han<PERSON><PERSON><PERSON><PERSON>eader
from improved_dicom_reader import ImprovedDI<PERSON>MReader

# 尝试导入PyRadiomics
try:
    from pyradiomics_dicom_reader import PyRadiomicsDICOMReader
    PYRADIOMICS_AVAILABLE = True
except ImportError:
    PYRADIOMICS_AVAILABLE = False

class DICOMReaderComparison:
    """DICOM读取器对比测试"""
    
    def __init__(self):
        self.readers = {
            'native': NativeDICOMReader(),
            'enhanced': EnhancedDICOMReader(),
            'improved': ImprovedDICOMReader()
        }
        
        if PYRADIOMICS_AVAILABLE:
            self.readers['pyradiomics'] = PyRadiomicsDICOMReader()
    
    def test_single_file(self, file_path: Path) -> Dict:
        """测试单个文件的读取性能"""
        results = {}
        
        print(f"\n=== 测试文件: {file_path.name} ===")
        print(f"文件大小: {file_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        for name, reader in self.readers.items():
            print(f"\n--- 测试 {name.upper()} 读取器 ---")
            
            try:
                start_time = time.time()
                
                if name == 'native':
                    # 测试原生读取器
                    if reader.is_dicom_file(file_path):
                        info = reader.read_dicom_header(file_path)
                        pixel_data = reader.extract_pixel_data(file_path, info) if info else None
                        success = pixel_data is not None
                        data_size = len(pixel_data) if pixel_data else 0
                    else:
                        success = False
                        data_size = 0
                        info = None
                
                elif name == 'enhanced':
                    # 测试增强读取器
                    pixel_info = reader.read_dicom_raw(file_path)
                    success = pixel_info is not None and pixel_info.get('pixel_data') is not None
                    data_size = pixel_info.get('data_length', 0) if pixel_info else 0
                    info = pixel_info
                
                elif name == 'improved':
                    # 测试改进读取器
                    dicom_info = reader.read_dicom_comprehensive(file_path)
                    success = dicom_info is not None and dicom_info.get('pixel_data') is not None
                    data_size = dicom_info.get('pixel_data_length', 0) if dicom_info else 0
                    info = dicom_info
                
                elif name == 'pyradiomics':
                    # 测试PyRadiomics读取器
                    if hasattr(reader, 'available') and reader.available:
                        dicom_info = reader.read_dicom_image(file_path)
                        success = dicom_info is not None and dicom_info.get('pixel_array') is not None
                        data_size = dicom_info.get('pixel_array').nbytes if dicom_info and dicom_info.get('pixel_array') is not None else 0
                        info = dicom_info
                    else:
                        success = False
                        data_size = 0
                        info = None
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                results[name] = {
                    'success': success,
                    'processing_time': processing_time,
                    'data_size': data_size,
                    'info': info
                }
                
                # 输出结果
                status = "✅ 成功" if success else "❌ 失败"
                print(f"状态: {status}")
                print(f"处理时间: {processing_time:.3f} 秒")
                print(f"数据大小: {data_size / 1024 / 1024:.2f} MB" if data_size > 0 else "数据大小: 0 MB")
                
                if success and info:
                    if name == 'pyradiomics' and 'image_size' in info:
                        print(f"图像尺寸: {info['image_size']}")
                        print(f"像素类型: {info.get('pixel_type', 'Unknown')}")
                        print(f"值范围: {info.get('min_value', 0):.1f} - {info.get('max_value', 0):.1f}")
                    elif 'rows' in info and 'columns' in info:
                        print(f"图像尺寸: {info['columns']}×{info['rows']}")
                        print(f"位深度: {info.get('bits_allocated', 'Unknown')}")
                        print(f"窗宽窗位: {info.get('window_center', 'Unknown')}/{info.get('window_width', 'Unknown')}")
                
            except Exception as e:
                results[name] = {
                    'success': False,
                    'processing_time': 0,
                    'data_size': 0,
                    'error': str(e)
                }
                print(f"❌ 错误: {e}")
        
        return results
    
    def test_image_generation(self, file_path: Path) -> Dict:
        """测试图像生成性能"""
        print(f"\n=== 测试图像生成: {file_path.name} ===")
        
        generation_results = {}
        
        for name, reader in self.readers.items():
            print(f"\n--- 测试 {name.upper()} 图像生成 ---")
            
            try:
                start_time = time.time()
                
                if name == 'native':
                    if reader.is_dicom_file(file_path):
                        info = reader.read_dicom_header(file_path)
                        if info:
                            image_data = reader.create_simulated_medical_image(
                                info.get('columns', 512), 
                                info.get('rows', 512),
                                40, 400
                            )
                        else:
                            image_data = None
                    else:
                        image_data = None
                
                elif name == 'enhanced':
                    pixel_info = reader.read_dicom_raw(file_path)
                    if pixel_info and pixel_info.get('pixel_data'):
                        image_data = reader.create_image_from_raw_pixels(pixel_info, 40, 400)
                    else:
                        image_data = None
                
                elif name == 'improved':
                    dicom_info = reader.read_dicom_comprehensive(file_path)
                    if dicom_info and dicom_info.get('pixel_data'):
                        image_data = reader.create_real_medical_image(dicom_info, 40, 400)
                    else:
                        image_data = None
                
                elif name == 'pyradiomics':
                    if hasattr(reader, 'available') and reader.available:
                        dicom_info = reader.read_dicom_image(file_path)
                        if dicom_info and dicom_info.get('pixel_array') is not None:
                            image_data = reader.create_image_from_array(
                                dicom_info['pixel_array'], 40, 400
                            )
                        else:
                            image_data = None
                    else:
                        image_data = None
                
                end_time = time.time()
                generation_time = end_time - start_time
                
                success = image_data is not None and len(image_data) > 0
                image_size = len(image_data) if image_data else 0
                
                generation_results[name] = {
                    'success': success,
                    'generation_time': generation_time,
                    'image_size': image_size
                }
                
                status = "✅ 成功" if success else "❌ 失败"
                print(f"状态: {status}")
                print(f"生成时间: {generation_time:.3f} 秒")
                print(f"图像大小: {image_size / 1024:.1f} KB" if image_size > 0 else "图像大小: 0 KB")
                
            except Exception as e:
                generation_results[name] = {
                    'success': False,
                    'generation_time': 0,
                    'image_size': 0,
                    'error': str(e)
                }
                print(f"❌ 错误: {e}")
        
        return generation_results
    
    def run_comprehensive_test(self, test_files: List[Path]) -> Dict:
        """运行全面测试"""
        print("🧪 DICOM读取器全面对比测试")
        print("=" * 50)
        
        all_results = {
            'reading': {},
            'generation': {},
            'summary': {}
        }
        
        for file_path in test_files:
            if file_path.exists():
                # 测试读取性能
                reading_results = self.test_single_file(file_path)
                all_results['reading'][file_path.name] = reading_results
                
                # 测试图像生成
                generation_results = self.test_image_generation(file_path)
                all_results['generation'][file_path.name] = generation_results
            else:
                print(f"⚠️ 文件不存在: {file_path}")
        
        # 生成总结
        self.generate_summary(all_results)
        
        return all_results
    
    def generate_summary(self, results: Dict):
        """生成测试总结"""
        print("\n" + "=" * 50)
        print("📊 测试总结")
        print("=" * 50)
        
        readers = list(self.readers.keys())
        
        # 成功率统计
        print("\n🎯 成功率对比:")
        for reader in readers:
            reading_success = 0
            generation_success = 0
            total_tests = 0
            
            for file_results in results['reading'].values():
                if reader in file_results:
                    total_tests += 1
                    if file_results[reader]['success']:
                        reading_success += 1
            
            for file_results in results['generation'].values():
                if reader in file_results:
                    if file_results[reader]['success']:
                        generation_success += 1
            
            if total_tests > 0:
                reading_rate = reading_success / total_tests * 100
                generation_rate = generation_success / total_tests * 100
                print(f"{reader.upper():12}: 读取 {reading_rate:5.1f}% | 生成 {generation_rate:5.1f}%")
        
        # 性能对比
        print("\n⚡ 平均处理时间:")
        for reader in readers:
            total_time = 0
            count = 0
            
            for file_results in results['reading'].values():
                if reader in file_results and file_results[reader]['success']:
                    total_time += file_results[reader]['processing_time']
                    count += 1
            
            if count > 0:
                avg_time = total_time / count
                print(f"{reader.upper():12}: {avg_time:.3f} 秒")
        
        # 数据提取能力
        print("\n💾 数据提取能力:")
        for reader in readers:
            total_data = 0
            count = 0
            
            for file_results in results['reading'].values():
                if reader in file_results and file_results[reader]['success']:
                    total_data += file_results[reader]['data_size']
                    count += 1
            
            if count > 0:
                avg_data = total_data / count / 1024 / 1024
                print(f"{reader.upper():12}: 平均 {avg_data:.2f} MB")

def main():
    """主测试函数"""
    # 测试文件列表
    test_files = [
        Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-001.dcm"),
        Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-010.dcm"),
        Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-020.dcm")
    ]
    
    # 运行对比测试
    comparison = DICOMReaderComparison()
    results = comparison.run_comprehensive_test(test_files)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
