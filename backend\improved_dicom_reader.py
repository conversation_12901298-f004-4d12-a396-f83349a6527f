#!/usr/bin/env python3
"""改进的DICOM读取器，不依赖外部库，专注于真实像素数据提取"""

import struct
import base64
from pathlib import Path
from typing import Dict, Optional, List, Tuple

class ImprovedDICOMReader:
    """改进的DICOM读取器，专注于真实像素数据显示"""
    
    def __init__(self):
        self.dicom_prefix = b'DICM'
        # DICOM传输语法
        self.transfer_syntaxes = {
            '1.2.840.10008.1.2': 'Implicit VR Little Endian',
            '1.2.840.10008.1.2.1': 'Explicit VR Little Endian',
            '1.2.840.10008.1.2.2': 'Explicit VR Big Endian'
        }
    
    def read_dicom_comprehensive(self, file_path: Path) -> Optional[Dict]:
        """全面读取DICOM文件"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 查找DICM标识
            dicm_pos = data.find(self.dicom_prefix)
            if dicm_pos == -1:
                return None
            
            # 解析DICOM数据
            dicom_info = self._parse_dicom_data(data, dicm_pos + 4, file_path.name)
            
            if dicom_info and dicom_info.get('pixel_data'):
                print(f"✅ 成功提取DICOM数据: {len(dicom_info['pixel_data'])} 字节像素数据")
                return dicom_info
            
            return None
            
        except Exception as e:
            print(f"Error reading DICOM comprehensively: {e}")
            return None
    
    def _parse_dicom_data(self, data: bytes, start_pos: int, filename: str) -> Optional[Dict]:
        """解析DICOM数据元素"""
        pos = start_pos
        dicom_info = {
            'filename': filename,
            'rows': 512,
            'columns': 512,
            'bits_allocated': 16,
            'pixel_representation': 0,
            'window_center': 40,
            'window_width': 400,
            'pixel_data': None,
            'transfer_syntax': 'Unknown'
        }
        
        # 读取数据元素
        while pos < len(data) - 8:
            try:
                # 读取标签
                if pos + 4 > len(data):
                    break
                
                group, element = struct.unpack('<HH', data[pos:pos+4])
                tag = (group, element)
                pos += 4
                
                # 检查是否是像素数据标签
                if tag == (0x7FE0, 0x0010):
                    pixel_info = self._extract_pixel_data_advanced(data, pos, dicom_info)
                    if pixel_info:
                        dicom_info.update(pixel_info)
                    break
                
                # 读取其他重要标签
                vr, length, value, new_pos = self._read_data_element(data, pos)
                if new_pos is None:
                    break
                
                pos = new_pos
                
                # 解析重要的标签值
                self._parse_important_tags(tag, value, dicom_info)
                
                # 限制解析范围，避免读取整个文件
                if pos > start_pos + 50000:  # 只读取前50KB的头部
                    break
                    
            except (struct.error, IndexError):
                break
        
        return dicom_info
    
    def _read_data_element(self, data: bytes, pos: int) -> Tuple[str, int, bytes, Optional[int]]:
        """读取DICOM数据元素"""
        try:
            # 读取VR
            if pos + 2 > len(data):
                return '', 0, b'', None
            
            vr = data[pos:pos+2].decode('ascii', errors='ignore')
            pos += 2
            
            # 根据VR确定长度字段
            if vr in ['OB', 'OW', 'OF', 'SQ', 'UT', 'UN']:
                # 显式VR，长度为4字节
                if pos + 6 > len(data):
                    return vr, 0, b'', None
                pos += 2  # 跳过保留字节
                length = struct.unpack('<I', data[pos:pos+4])[0]
                pos += 4
            else:
                # 显式VR，长度为2字节
                if pos + 2 > len(data):
                    return vr, 0, b'', None
                length = struct.unpack('<H', data[pos:pos+2])[0]
                pos += 2
            
            # 读取值
            if length > 0 and pos + length <= len(data):
                value = data[pos:pos+length]
                return vr, length, value, pos + length
            else:
                return vr, length, b'', pos + max(0, min(length, len(data) - pos))
                
        except:
            return '', 0, b'', None
    
    def _parse_important_tags(self, tag: Tuple[int, int], value: bytes, dicom_info: Dict):
        """解析重要的DICOM标签"""
        try:
            if tag == (0x0028, 0x0010):  # Rows
                if len(value) >= 2:
                    dicom_info['rows'] = struct.unpack('<H', value[:2])[0]
            elif tag == (0x0028, 0x0011):  # Columns
                if len(value) >= 2:
                    dicom_info['columns'] = struct.unpack('<H', value[:2])[0]
            elif tag == (0x0028, 0x0100):  # BitsAllocated
                if len(value) >= 2:
                    dicom_info['bits_allocated'] = struct.unpack('<H', value[:2])[0]
            elif tag == (0x0028, 0x0103):  # PixelRepresentation
                if len(value) >= 2:
                    dicom_info['pixel_representation'] = struct.unpack('<H', value[:2])[0]
            elif tag == (0x0028, 0x1050):  # WindowCenter
                try:
                    wc_str = value.decode('ascii', errors='ignore').strip('\x00 ')
                    if wc_str:
                        dicom_info['window_center'] = float(wc_str.split('\\')[0])
                except:
                    pass
            elif tag == (0x0028, 0x1051):  # WindowWidth
                try:
                    ww_str = value.decode('ascii', errors='ignore').strip('\x00 ')
                    if ww_str:
                        dicom_info['window_width'] = float(ww_str.split('\\')[0])
                except:
                    pass
        except:
            pass
    
    def _extract_pixel_data_advanced(self, data: bytes, pos: int, dicom_info: Dict) -> Optional[Dict]:
        """高级像素数据提取"""
        try:
            # 读取像素数据的VR和长度
            vr, length, pixel_data, _ = self._read_data_element(data, pos)
            
            if not pixel_data or len(pixel_data) < 1000:
                return None
            
            return {
                'pixel_data': pixel_data,
                'pixel_data_length': len(pixel_data),
                'pixel_vr': vr
            }
            
        except Exception as e:
            print(f"Error extracting advanced pixel data: {e}")
            return None
    
    def create_real_medical_image(self, dicom_info: Dict, window_center: float = None, 
                                window_width: float = None) -> str:
        """创建真实的医学图像"""
        try:
            pixel_data = dicom_info['pixel_data']
            rows = dicom_info['rows']
            columns = dicom_info['columns']
            bits_allocated = dicom_info['bits_allocated']
            
            # 使用提供的窗宽窗位或DICOM文件中的值
            wc = window_center if window_center is not None else dicom_info['window_center']
            ww = window_width if window_width is not None else dicom_info['window_width']
            
            # 解析像素值
            pixels = self._parse_pixel_values(pixel_data, rows, columns, bits_allocated)
            
            if not pixels:
                return self._create_fallback_image(dicom_info['filename'])
            
            # 应用窗宽窗位并创建图像
            return self._create_windowed_image_svg(pixels, rows, columns, wc, ww)
            
        except Exception as e:
            print(f"Error creating real medical image: {e}")
            return self._create_fallback_image(dicom_info.get('filename', 'unknown'))
    
    def _parse_pixel_values(self, pixel_data: bytes, rows: int, columns: int, 
                          bits_allocated: int) -> Optional[List[int]]:
        """解析像素值"""
        try:
            pixels = []
            expected_pixels = rows * columns
            bytes_per_pixel = bits_allocated // 8
            
            if bits_allocated == 16:
                # 16位像素
                for i in range(0, min(len(pixel_data), expected_pixels * 2), 2):
                    if i + 1 < len(pixel_data):
                        pixel_value = struct.unpack('<H', pixel_data[i:i+2])[0]
                        pixels.append(pixel_value)
                    else:
                        pixels.append(0)
            else:
                # 8位像素
                for i in range(min(len(pixel_data), expected_pixels)):
                    pixels.append(pixel_data[i])
            
            # 填充不足的像素
            while len(pixels) < expected_pixels:
                pixels.append(0)
            
            return pixels[:expected_pixels]
            
        except Exception as e:
            print(f"Error parsing pixel values: {e}")
            return None
    
    def _create_windowed_image_svg(self, pixels: List[int], rows: int, columns: int,
                                 window_center: float, window_width: float) -> str:
        """创建应用窗宽窗位的SVG图像"""
        try:
            # 计算像素统计
            min_pixel = min(pixels) if pixels else 0
            max_pixel = max(pixels) if pixels else 255
            
            # 应用窗宽窗位变换
            min_window = window_center - window_width / 2
            max_window = window_center + window_width / 2
            
            # 处理像素值
            processed_pixels = []
            for pixel in pixels:
                if pixel <= min_window:
                    gray = 0
                elif pixel >= max_window:
                    gray = 255
                else:
                    gray = int(255 * (pixel - min_window) / window_width)
                processed_pixels.append(max(0, min(255, gray)))
            
            # 创建SVG
            display_size = 512
            svg_content = f'''<svg width="{display_size}" height="{display_size}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000"/>
                <g>'''
            
            # 采样显示像素
            step = max(1, max(rows, columns) // display_size)
            
            for y in range(0, rows, step):
                for x in range(0, columns, step):
                    pixel_index = y * columns + x
                    if pixel_index < len(processed_pixels):
                        gray_val = processed_pixels[pixel_index]
                        color = f"#{gray_val:02x}{gray_val:02x}{gray_val:02x}"
                        
                        svg_x = x * display_size // columns
                        svg_y = y * display_size // rows
                        size = max(1, step * display_size // max(rows, columns))
                        
                        svg_content += f'<rect x="{svg_x}" y="{svg_y}" width="{size}" height="{size}" fill="{color}"/>'
            
            # 添加信息叠加
            svg_content += f'''
                </g>
                <!-- 信息叠加 -->
                <rect x="10" y="10" width="220" height="100" fill="#000" opacity="0.8" rx="5"/>
                <text x="20" y="30" font-family="monospace" font-size="12" fill="#0f0">真实DICOM像素数据</text>
                <text x="20" y="45" font-family="monospace" font-size="10" fill="#0a0">尺寸: {columns}×{rows}</text>
                <text x="20" y="60" font-family="monospace" font-size="10" fill="#0a0">范围: {min_pixel}-{max_pixel}</text>
                <text x="20" y="75" font-family="monospace" font-size="10" fill="#0a0">WC:{window_center:.0f} WW:{window_width:.0f}</text>
                <text x="20" y="90" font-family="monospace" font-size="10" fill="#0a0">改进读取器</text>
            </svg>'''
            
            # 转换为base64
            svg_bytes = svg_content.encode('utf-8')
            svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
            
            return f"data:image/svg+xml;base64,{svg_base64}"
            
        except Exception as e:
            print(f"Error creating windowed image SVG: {e}")
            return self._create_fallback_image("SVG creation error")
    
    def _create_fallback_image(self, filename: str) -> str:
        """创建后备图像"""
        svg_content = f'''<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#1a1a1a"/>
            <text x="50%" y="45%" text-anchor="middle" font-family="monospace" font-size="16" fill="#f44">
                像素数据解析失败
            </text>
            <text x="50%" y="55%" text-anchor="middle" font-family="monospace" font-size="12" fill="#f88">
                {filename}
            </text>
        </svg>'''
        
        svg_bytes = svg_content.encode('utf-8')
        svg_base64 = base64.b64encode(svg_bytes).decode('utf-8')
        
        return f"data:image/svg+xml;base64,{svg_base64}"

# 测试函数
def test_improved_reader():
    """测试改进的读取器"""
    reader = ImprovedDICOMReader()
    
    test_file = Path("../sourcedata/a/manifest-1603198545583/NSCLC-Radiomics/LUNG1-001/01-01-2000-StudyID-NA-30178/3.000000-NA-03192/1-001.dcm")
    
    if test_file.exists():
        print(f"测试文件: {test_file.name}")
        
        dicom_info = reader.read_dicom_comprehensive(test_file)
        if dicom_info:
            print(f"✅ 读取成功:")
            print(f"   尺寸: {dicom_info['columns']}×{dicom_info['rows']}")
            print(f"   位深: {dicom_info['bits_allocated']}")
            print(f"   像素数据: {dicom_info['pixel_data_length']} 字节")
            print(f"   窗宽窗位: {dicom_info['window_center']}/{dicom_info['window_width']}")
            
            # 生成图像
            image_data = reader.create_real_medical_image(dicom_info)
            print(f"✅ 生成图像: {len(image_data)} 字符")
        else:
            print("❌ 读取失败")
    else:
        print(f"❌ 文件不存在: {test_file}")

if __name__ == "__main__":
    test_improved_reader()
