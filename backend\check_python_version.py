#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys

print(f"Python版本: {sys.version}")
print(f"Python版本信息: {sys.version_info}")
print(f"主版本: {sys.version_info.major}")
print(f"次版本: {sys.version_info.minor}")
print(f"修订版本: {sys.version_info.micro}")

# 检查PyRadiomics兼容性
major = sys.version_info.major
minor = sys.version_info.minor

print("\n🔍 PyRadiomics兼容性检查:")
print("PyRadiomics 3.1.0支持的Python版本: 3.7-3.9")

if major == 3 and 7 <= minor <= 9:
    print("✅ 当前Python版本与PyRadiomics兼容")
elif major == 3 and minor > 9:
    print("❌ 当前Python版本过高，PyRadiomics不支持")
    print(f"   当前版本: Python {major}.{minor}")
    print("   建议: 使用Python 3.7-3.9版本")
elif major == 3 and minor < 7:
    print("❌ 当前Python版本过低，PyRadiomics不支持")
    print(f"   当前版本: Python {major}.{minor}")
    print("   建议: 升级到Python 3.7-3.9版本")
else:
    print("❌ 不支持的Python版本")
    print(f"   当前版本: Python {major}.{minor}")
    print("   PyRadiomics需要Python 3.7-3.9")

print("\n💡 解决方案:")
if major == 3 and minor > 9:
    print("1. 安装Python 3.9版本")
    print("2. 使用conda创建Python 3.9环境")
    print("3. 使用pyenv切换到Python 3.9")
    print("4. 继续使用当前的PyDicom模拟器（已读取真实DICOM数据）")
